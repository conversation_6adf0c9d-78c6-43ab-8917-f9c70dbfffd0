package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsInProgressEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsInProgressPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyDuplicateRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyDuplicateRawDataService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateProductsInProgressListener implements
        ApplicationEventListener<GenerateProductsInProgressEvent, GenerateProductsInProgressPayload> {

    private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private final MasterCatalogPotentiallyDuplicateRawDataService masterCatalogPotentiallyDuplicateRawDataService;
    private final MasterCatalogPotentiallyDuplicateRawDataMapper masterCatalogPotentiallyDuplicateRawDataMapper;
    private final ApplicationEventPublisherProvider applicationEventPublisherProvider;

    @Transactional
    @Override
    public void handleEvent(GenerateProductsInProgressEvent event) {
        log.info("GENERATE_PRODUCTS_IN_PROGRESS listener start handle event.");
        GenerateProductsInProgressPayload payload = event.getPayload();
        MasterCatalogBatchJobDto jobDto = payload.getData();
        List<List<UUID>> rawDataIds = payload.getRawDataIds();
        UUID jobId = jobDto.getId();
        MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(
                jobDto);

        List<MasterCatalogPotentiallyDuplicateRawDataDto> savedDuplicateRawDataList = masterCatalogPotentiallyDuplicateRawDataService
                .processPotentiallyDuplicateItem(
                        rawDataIds, jobId,
                        PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED, MasterCatalogTaskType.DUPLICATION_WITH_PRODUCT);
        updateJobStatus(job);

        // Check if the status are all reviewed, send the generate product submit event
        // directly
        // In this case, we don't need to wait for the review
        if (savedDuplicateRawDataList.stream().allMatch(MasterCatalogPotentiallyDuplicateRawDataDto::isReviewed)) {
            List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit = savedDuplicateRawDataList.stream()
                    .map(masterCatalogPotentiallyDuplicateRawDataMapper::toEntity)
                    .toList();
            applicationEventPublisherProvider.generateProductsSubmitEvent(job, validToSubmit);
        }
    }

    void updateJobStatus(MasterCatalogBatchJob job) {
        job.setStatus(MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_IN_PROGRESS);
        masterCatalogBatchJobRepository.save(job);
        log.info("Job {} remove duplication start with status {}", job.getId(), job.getStatus());
    }

}
