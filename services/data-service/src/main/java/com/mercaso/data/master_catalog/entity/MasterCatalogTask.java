package com.mercaso.data.master_catalog.entity;

import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "mc_task", schema = "public")
public class MasterCatalogTask extends BaseEntity{

  @NotNull
  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private MasterCatalogTaskStatus status;

  @NotNull
  @Column(name = "type")
  @Enumerated(EnumType.STRING)
  private MasterCatalogTaskType type;

  @Column(name = "job_id", nullable = false)
  private UUID jobId;

  @Column(name = "assigned_by")
  private String assignedBy;

  @Column(name = "assigned_to")
  private String assignedTo;

  @Column(name = "task_number", nullable = false)
  private String taskNumber;
}
