package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.DEFAULT_BATCH_SIZE;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.service.MasterCatalogBatchJobService;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/batch-jobs")
@RequiredArgsConstructor
@Validated
public class MasterCatalogBatchJobController {

    private final MasterCatalogBatchJobService batchJobService;

    @PreAuthorize("hasAnyAuthority('master-catalog:write:batch-jobs')")
    @PostMapping("/start")
    public ResponseEntity<Void> startProcessing(
        @RequestParam(value = "batchSize", defaultValue = DEFAULT_BATCH_SIZE) Integer batchSize) {
        batchJobService.startInitialProcessing(batchSize);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:read:batch-jobs')")
    @GetMapping("/search")
    public CustomPage<MasterCatalogBatchJobListDto> search(
        @RequestParam(value = "status", required = false) MasterCatalogBatchJobStatus status,
        @Min(1) @RequestParam(value = "page") Integer page,
        @Min(1) @RequestParam(value = "pageSize") Integer pageSize) {
        PageRequest pageRequest = PageRequest.of(page - 1, pageSize);
        Page<MasterCatalogBatchJobListDto> masterCatalogBatchJobList = batchJobService.search(status, pageRequest);
        return new CustomPage<MasterCatalogBatchJobListDto>().build(masterCatalogBatchJobList);
    }
}
