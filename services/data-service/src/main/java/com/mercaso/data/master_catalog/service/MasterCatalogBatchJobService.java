package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface MasterCatalogBatchJobService {

    void startInitialProcessing(Integer batchSize);

    Page<MasterCatalogBatchJobListDto> search(MasterCatalogBatchJobStatus status, PageRequest pageRequest);
}
