package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class MasterCatalogTaskDto extends BaseDto{

  private UUID id;

  private MasterCatalogTaskStatus status;

  private MasterCatalogTaskType type;

  private UUID jobId;

  private String assignedBy;

  private String assignedTo;

  private String taskNumber;

  private Instant createdAt;

  private Instant updatedAt;
}
