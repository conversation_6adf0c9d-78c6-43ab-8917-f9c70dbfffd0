package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.AVAILABLE_GENERATE_PRODUCTS_STATUSES;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsSubmitEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsSubmitPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePotentialDuplicationService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class GenerateProductsSubmitEventListener extends AbstractSubmitDuplicationEventListener implements
    ApplicationEventListener<GenerateProductsSubmitEvent, GenerateProductsSubmitPayload> {

    private final MasterCatalogRawDataService masterCatalogRawDataService;
    private final MasterCatalogProductRepository masterCatalogProductRepository;
    private final MasterCatalogGeneratePotentialDuplicationService masterCatalogGeneratePotentialDuplicationService;
    public GenerateProductsSubmitEventListener(
        MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository,
        MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository,
        MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService,
        MasterCatalogBatchJobRepository masterCatalogBatchJobRepository,
        MasterCatalogBatchJobMapper masterCatalogBatchJobMapper,
        ApplicationEventPublisherProvider applicationEventPublisherProvider,
        MasterCatalogRawDataService masterCatalogRawDataService, MasterCatalogRawDataRepository masterCatalogRawDataRepository,
        MasterCatalogProductRepository masterCatalogProductRepository,
        MasterCatalogTaskRepository masterCatalogTaskRepository,
        MasterCatalogGeneratePotentialDuplicationService masterCatalogGeneratePotentialDuplicationService) {
        super(masterCatalogPotentiallyDuplicateRawDataRepository, masterCatalogRawDataDuplicationRepository,
            masterCatalogRawDataDuplicationService, masterCatalogBatchJobRepository,
            masterCatalogBatchJobMapper, applicationEventPublisherProvider, masterCatalogRawDataRepository, masterCatalogTaskRepository);
        this.masterCatalogRawDataService = masterCatalogRawDataService;
        this.masterCatalogProductRepository = masterCatalogProductRepository;
        this.masterCatalogGeneratePotentialDuplicationService = masterCatalogGeneratePotentialDuplicationService;
    }

    @Transactional
    @Override
    public void handleEvent(GenerateProductsSubmitEvent event) {
        PotentiallyDuplicationContext context = createContext(event.getPayload());
        processMasterCatalogTask(context.potentiallyDuplicateData());
        processGenerateProductsSubmit(context);
    }

    private void processGenerateProductsSubmit(PotentiallyDuplicationContext context) {
        List<MasterCatalogPotentiallyDuplicateRawData> potentiallyDuplicateList = context.potentiallyDuplicateData();
        if (anyMatchUnreviewedStatus(potentiallyDuplicateList)) {
            log.info("Job {} contains unreviewed data, processing skipped", context.jobId());
            return;
        }

        List<MasterCatalogPotentiallyDuplicateRawData> markedDuplicates = getMarkedDuplicates(potentiallyDuplicateList);

        if (!markedDuplicates.isEmpty()) {
            log.info("Job {} contains marked duplicates, processing duplication records [{}]",
                context.jobId(),
                markedDuplicates.size());
            processDuplicationRecords(context.jobId(), markedDuplicates);
        }

        List<MasterCatalogPotentiallyDuplicateRawData> allPotentiallyDuplicateRawData = masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(
            context.jobId());

        boolean allSubmitted = allPotentiallyDuplicateRawData.stream()
            .allMatch(data -> AVAILABLE_GENERATE_PRODUCTS_STATUSES.contains(data.getStatus()));

        if (allSubmitted) {
            updateJobStatus(context);

            Set<UUID> toBeProductsRawDataIds = getToBeProductsRawDataIds(allPotentiallyDuplicateRawData);

            final Set<UUID> toBeCompletedRawDataIds = new HashSet<>(toBeProductsRawDataIds);

            List<MasterCatalogProduct> newProductList = generateNewProducts(toBeProductsRawDataIds);

            masterCatalogRawDataService.markAsCompleted(toBeCompletedRawDataIds);

            masterCatalogGeneratePotentialDuplicationService.pushProduct(
                newProductList, "prod-product-table");
        }
    }

    private List<MasterCatalogProduct> generateNewProducts(Set<UUID> toBeProductsRawDataIds) {

        List<MasterCatalogRawData> toBeProductsRawData = masterCatalogRawDataRepository.findAllById(toBeProductsRawDataIds);

        List<MasterCatalogProduct> products = new ArrayList<>();
        toBeProductsRawData.forEach(m -> products.add(buildMasterCatalogProduct(m)));

        masterCatalogProductRepository.saveAll(products);
        return products;
    }

    private Set<UUID> getToBeProductsRawDataIds(List<MasterCatalogPotentiallyDuplicateRawData> allPotentiallyDuplicateRawData) {
        List<MasterCatalogPotentiallyDuplicateRawData> secondRoundReviewedData = allPotentiallyDuplicateRawData.stream()
            .filter(data -> data.getStatus() == PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED)
            .toList();

        // Identified potential duplicates labeled as non-duplicates.
        Set<UUID> toBeProductsRawDataIds = secondRoundReviewedData.stream()
            .filter(data -> BooleanUtils.isNotTrue(data.getDuplicated()))
            .map(MasterCatalogPotentiallyDuplicateRawData::getPotentiallyDuplicateRawDataId)
            .filter(Objects::nonNull).collect(Collectors.toSet());

        return toBeProductsRawDataIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
    }

    private MasterCatalogProduct buildMasterCatalogProduct(MasterCatalogRawData m) {
        return MasterCatalogProduct.builder()
            .name(m.getName())
            .description(m.getDescription())
            .upc(m.getUpc())
            .skuNumber(m.getSkuNumber())
            .brand(m.getBrand())
            .category(m.getCategory())
            .subCategory(m.getSubCategory())
            .department(m.getDepartment())
            .clazz(m.getClazz())
            .primaryVendor(m.getPrimaryVendor())
            .masterCatalogRawDataId(m.getId())
            .build();
    }

    private void processDuplicationRecords(UUID jobId, List<MasterCatalogPotentiallyDuplicateRawData> markedDuplicates) {
        List<MasterCatalogRawDataDuplication> duplicationRecords = createDuplicationRecords(markedDuplicates);

        if (!duplicationRecords.isEmpty()) {
            masterCatalogRawDataDuplicationRepository.saveAll(duplicationRecords);
            log.info("Saved {} duplication records for job {}", duplicationRecords.size(), jobId);
        }
    }

    private PotentiallyDuplicationContext createContext(GenerateProductsSubmitPayload payload) {
        MasterCatalogBatchJobDto jobDto = payload.getData();
        MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(jobDto);
        List<MasterCatalogPotentiallyDuplicateRawData> potentiallyDuplicateData = masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(
            jobDto.getId());

        return new PotentiallyDuplicationContext(jobDto.getId(), job, potentiallyDuplicateData);
    }

    @Override
    protected boolean anyMatchUnreviewedStatus(List<MasterCatalogPotentiallyDuplicateRawData> duplicateData) {
        return duplicateData.stream()
            .filter(data -> data.getStatus() != PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED)
            .anyMatch(data -> data.getStatus() != PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED);
    }

    @Override
    protected void updateJobStatus(PotentiallyDuplicationContext context) {
        MasterCatalogBatchJob job = context.job();
        job.setStatus(MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_COMPLETED);
        job.setCompletedAt(Instant.now());
        masterCatalogBatchJobRepository.save(job);
        log.info("Job {} generate products completed with status {}", job.getId(), job.getStatus());
    }
}

