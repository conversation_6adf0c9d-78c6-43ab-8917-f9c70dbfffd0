package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PotentiallyDuplicateRawDataRequest {

    private int page = 1;
    private int pageSize = 20;
    @NotNull(message = "taskId must be not null")
    private String taskId;
    private String status;

    @AssertTrue(message = "The status is invalid, only can use one of the following: PENDING_REVIEW, IN_STAGE")
    public boolean isStatusValid() {
        return StringUtils.isEmpty(status) || MasterCatalogBatchJobConstants.PENDING_STATUSES.stream()
            .anyMatch(s -> s.name().equals(status));
    }
}
