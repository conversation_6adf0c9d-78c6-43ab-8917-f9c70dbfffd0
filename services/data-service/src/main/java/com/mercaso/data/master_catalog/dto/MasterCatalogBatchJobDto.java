package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link MasterCatalogBatchJob}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogBatchJobDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private String jobNumber;
    private MasterCatalogBatchJobStatus status;
    private Instant completedAt;
}