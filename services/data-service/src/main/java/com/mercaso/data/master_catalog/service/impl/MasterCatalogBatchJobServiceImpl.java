package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.constants.CommonConstants;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.entity.MasterCatalogUpcPool;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.event.applicationevent.BusinessApplicationEventType;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventPublisher;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationStartPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository.ProductUpcAndName;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogUpcPoolRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import com.mercaso.data.master_catalog.service.MasterCatalogBatchJobService;
import com.mercaso.data.utils.BusinessNumberGenerator;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class MasterCatalogBatchJobServiceImpl implements MasterCatalogBatchJobService {

    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private final MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final MasterCatalogUpcPoolRepository masterCatalogUpcPoolRepository;
    private final MasterCatalogProductRepository masterCatalogProductRepository;
    private final MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService;
    private final MasterCatalogTaskRepository masterCatalogTaskRepository;

    @Transactional
    @Override
    public void startInitialProcessing(Integer batchSize) {
        log.info("Starting initial processing");

        if (hasInProgressJob()) {
            log.info("Exist in progress job, skip start initial processing");
            return;
        }

        MasterCatalogBatchJobDto newJob = createNewJob();
        processRawData(newJob, batchSize);
    }

    private boolean hasInProgressJob() {
        return masterCatalogBatchJobRepository
            .existsAllByStatusIsNot(MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_COMPLETED);
    }

    private void processRawData(MasterCatalogBatchJobDto job, Integer batchSize) {
        UUID JobId = job.getId();
        log.info("Starting raw data processing for job {}", JobId);

        List<MasterCatalogRawData> rawDataList = fetchDraftRawData(batchSize);
        log.debug("Fetched {} draft raw data records for job {}", rawDataList.size(), JobId);

        if (rawDataList.isEmpty()) {
            log.warn("No draft raw data found for processing, job {}", JobId);
            return;
        }

        // Group the raw data by UPC, key is the UPC, value is the list of raw data
        Map<String, List<MasterCatalogRawData>> rawDataMap =
            rawDataList.stream().collect(Collectors.groupingBy(MasterCatalogRawData::getUpc));

        // Get the first raw data in the list for each UPC and add them to the unique raw data list
        List<MasterCatalogRawData> uniqueRawDataList =
            new ArrayList<>(rawDataMap.values().stream().map(List::getFirst).toList());

        // Get the upc list from the unique raw data list
        List<String> uniqueUpcList = uniqueRawDataList.stream().map(MasterCatalogRawData::getUpc).toList();

        // Use the optimized batch method instead of the original query
        List<String> uniqueUpcsNotInPool = findAllUpcsNotInPool(uniqueUpcList);

        // Remove the raw data from unique raw data list if the upc is in the upc pool list
        uniqueRawDataList.removeIf(rawData -> !uniqueUpcsNotInPool.contains(rawData.getUpc()));

        List<UUID> needToBeProcessedRawDataIds =
            uniqueRawDataList.stream().map(MasterCatalogRawData::getId).collect(Collectors.toList());

        // Group the unique raw data list by name ignore case if name is not null or empty, key is
        // the lower case of the name, value is the list of raw data
        Map<String, List<MasterCatalogRawData>> rawDatasGroupByName =
            uniqueRawDataList.stream().filter(rawData -> StringUtils.isNotBlank(rawData.getName()))
                .collect(Collectors.groupingBy(rawData -> rawData.getName().toLowerCase()));

        List<String> names = rawDatasGroupByName.keySet().stream().toList();

        Map<String, List<String>> nameToUpcsMap = getUpcsByNameFromProduct(names);

        for (Entry<String, List<MasterCatalogRawData>> entry : rawDatasGroupByName.entrySet()) {
            String name = entry.getKey();
            List<MasterCatalogRawData> itemRawDataList = entry.getValue();
            List<String> referenceUpcList = nameToUpcsMap.get(name);

            List<MasterCatalogRawDataDuplication> duplicationList =
                masterCatalogRawDataDuplicationRepository.findAllUnderTheSameGroupByUpcIn(referenceUpcList);

            UUID duplicationGroup = masterCatalogRawDataDuplicationService.mergeDuplicationRecords(duplicationList);

            if (itemRawDataList.size() == 1 && duplicationGroup == null) {
                continue;
            }

            createRawDataDuplicationsBy(duplicationGroup, itemRawDataList);

            // Only keep the first item in the list for processing
            // Get the duplicates (all items except the first one)
            List<UUID> duplicateIds = itemRawDataList.stream().skip(1) // Skip the first item
                .map(MasterCatalogRawData::getId).toList();

            // Remove all duplicate IDs from the list of items to be processed
            needToBeProcessedRawDataIds.removeAll(duplicateIds);
        }

        // Update the raw data status to completed
        batchUpdateRawDataStatus(
            rawDataList.stream().filter(rawData -> !needToBeProcessedRawDataIds.contains(rawData.getId())).toList(),
            RawDataStatus.COMPLETED.name());

        if (!uniqueUpcsNotInPool.isEmpty()) {
            // Add all UPCs to the master catalog UPC pool.
            masterCatalogUpcPoolRepository
                .saveAll(uniqueUpcsNotInPool.stream().map(MasterCatalogUpcPool::new).toList());
        }

        // Publish event for further processing
        publishRemoveDuplicationStartEvent(job, needToBeProcessedRawDataIds);
    }

    private Map<String, List<String>> getUpcsByNameFromProduct(List<String> names) {
        // Get the product list from the names in batch

        List<ProductUpcAndName> productUpcAndNameList = new ArrayList<>();
        int batchSize = 200;
        for (int i = 0; i < names.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, names.size());
            List<String> batch = names.subList(i, endIndex);
            List<ProductUpcAndName> batchResult = masterCatalogProductRepository.findAllUpcsByName(batch);
            productUpcAndNameList.addAll(batchResult);
        }

        return productUpcAndNameList.stream().collect(Collectors.groupingBy(ProductUpcAndName::getName,
            Collectors.mapping(ProductUpcAndName::getUpc, Collectors.toList())));

    }

    private void batchUpdateRawDataStatus(List<MasterCatalogRawData> rawDataList, String status) {
        int batchSize = 200;
        for (int i = 0; i < rawDataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, rawDataList.size());
            List<MasterCatalogRawData> batch = rawDataList.subList(i, endIndex);
            batch.forEach(rawData -> {
                rawData.setStatus(status);
                if( RawDataStatus.COMPLETED.name().equals(status) ) {
                    rawData.setCompletedAt(Instant.now());
                }
            });
            masterCatalogRawDataRepository.saveAll(batch);
        }
    }

    private void createRawDataDuplicationsBy(UUID duplicationGroup, List<MasterCatalogRawData> rawDataList) {
        UUID duplicationGroupUid = duplicationGroup == null ? UUID.randomUUID() : duplicationGroup;
        List<MasterCatalogRawDataDuplication> duplicationList = rawDataList.stream().map(rawData -> {
            MasterCatalogRawDataDuplication duplication = new MasterCatalogRawDataDuplication();
            duplication.setId(UUID.randomUUID());
            duplication.setDuplicationGroup(duplicationGroupUid);
            duplication.setUpc(rawData.getUpc());
            duplication.setCreatedBy(CommonConstants.SYSTEM_USER_ID);
            return duplication;
        }).toList();

        masterCatalogRawDataDuplicationRepository.saveAll(duplicationList);
    }

    /**
     * Publish event for remove duplication process
     */
    private void publishRemoveDuplicationStartEvent(MasterCatalogBatchJobDto job,
        List<UUID> needToBeProcessed) {
        log.debug("Preparing remove duplication start event for job {}", job.getId());

        RemoveDuplicationStartPayload payload =
            RemoveDuplicationStartPayload.builder().batchJobId(job.getId())
                .rawDataIds(needToBeProcessed.stream().toList()).payload(job).build();

        applicationEventPublisher.publish(BusinessApplicationEventType.REMOVE_DUPLICATION_START, payload);

    }

    private MasterCatalogBatchJobDto createNewJob() {
        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(UUID.randomUUID());
        job.setJobNumber(BusinessNumberGenerator.generateJobNumber());
        job.setStatus(MasterCatalogBatchJobStatus.DRAFT);
        job.setCreatedAt(Instant.now());
        job.setUpdatedAt(Instant.now());

        MasterCatalogBatchJob savedJob = masterCatalogBatchJobRepository.save(job);
        log.info("Created new job with ID: {}", savedJob.getId());
        return masterCatalogBatchJobMapper.toDto(savedJob);
    }

    /**
     * Fetches draft raw data using pagination with 100 records per page until the total number of
     * records reaches the requested batch size.
     *
     * @param batchSize Maximum number of records to fetch
     * @return List of draft raw data up to the requested batch size
     */
    private List<MasterCatalogRawData> fetchDraftRawData(Integer batchSize) {
        List<MasterCatalogRawData> result = new ArrayList<>();
        final int pageSize = 100;
        int pageNumber = 0;

        Sort sort = Sort.by(Sort.Order.asc("createdAt"));
        while (result.size() < batchSize) {
            // Create a pageable request for the current page
            Pageable pageable = PageRequest.of(pageNumber, pageSize, sort);

            // Fetch one page of data
            Page<MasterCatalogRawData> page =
                masterCatalogRawDataRepository.findAllByStatus(RawDataStatus.DRAFT.name(), pageable);

            // Add the content to our result list
            List<MasterCatalogRawData> pageContent = page.getContent();
            result.addAll(pageContent);

            // If we've exceeded batchSize, trim the list
            if (result.size() > batchSize) {
                result = result.subList(0, batchSize);
            }

            // Break conditions:
            // 1. If the page is empty (no more data)
            // 2. If we've reached the last page
            // 3. If we've reached the batch size
            if (pageContent.isEmpty() || !page.hasNext() || result.size() >= batchSize) {
                break;
            }

            // Move to the next page
            pageNumber++;
        }

        return result;
    }

    /**
     * Fetches UPC pool entries in batches to handle large UPC lists that might exceed database IN
     * clause limitations
     *
     * @param uniqueUpcList List of UPCs to fetch
     * @return Combined list of all UPC pool entries found
     */
    private List<String> findAllUpcsNotInPool(List<String> uniqueUpcList) {
        // Define batch size
        final int batchSize = 100;

        // If UPC list is smaller than batch size, query directly
        if (uniqueUpcList.size() <= batchSize) {
            return masterCatalogUpcPoolRepository.findAllUpcsNotIn(String.join(",", uniqueUpcList));
        }

        // Otherwise, query in batches
        List<String> result = new ArrayList<>();

        for (int i = 0; i < uniqueUpcList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uniqueUpcList.size());
            List<String> batch = uniqueUpcList.subList(i, endIndex);
            List<String> batchResult = masterCatalogUpcPoolRepository.findAllUpcsNotIn(String.join(",", batch));
            result.addAll(batchResult);
        }

        return result;
    }

    public Page<MasterCatalogBatchJobListDto> search(MasterCatalogBatchJobStatus status, PageRequest pageRequest) {

        Specification<MasterCatalogBatchJob> specification = ((
            root, query, builder) -> {
            if (status != null) {
               return builder.equal(root.get("status"), status);
            }
            return builder.conjunction();
        });

        Page<MasterCatalogBatchJob> batchJobPage = masterCatalogBatchJobRepository.findAll(specification, pageRequest);
        List<MasterCatalogBatchJobListDto> batchJobs = batchJobPage.getContent().stream().map(masterCatalogBatchJobMapper::toListDto).toList();

        if (batchJobs.isEmpty()) {
            log.info("not any jobs found with status: {}", status);
        }
        List<UUID> jobIds = batchJobs.stream().map(MasterCatalogBatchJobListDto::getId).toList();
        Map<UUID, List<MasterCatalogTask>> allTaskMap = masterCatalogTaskRepository.findByJobIdIn(jobIds).stream()
            .collect(Collectors.groupingBy(MasterCatalogTask::getJobId));

        batchJobs.forEach(job -> {
            List<MasterCatalogTask> tasks = allTaskMap.getOrDefault(job.getId(), List.of());
            Map<MasterCatalogTaskType, List<MasterCatalogTask>> taskTypeMap = tasks.stream()
                .collect(Collectors.groupingBy(MasterCatalogTask::getType));

            MasterCatalogTaskType taskType = getTaskTypeByJobStatus(job.getStatus());
            List<MasterCatalogTask> taskList = taskTypeMap.getOrDefault(taskType, List.of());

            Map<MasterCatalogTaskStatus, Long> counts = taskList.stream()
                .collect(Collectors.groupingBy(MasterCatalogTask::getStatus, Collectors.counting()));

            job.setTaskType(taskType);
            job.setCurrentStageTaskCount(Math.toIntExact(taskList.size()));
            job.setCurrentStagePendingTaskCount(Math.toIntExact(counts.getOrDefault(MasterCatalogTaskStatus.PENDING, 0L)));
            job.setCurrentStageAssignedTaskCount(Math.toIntExact(counts.getOrDefault(MasterCatalogTaskStatus.ASSIGNED, 0L)));
            job.setCurrentStageInProcessTaskCount(Math.toIntExact(counts.getOrDefault(MasterCatalogTaskStatus.IN_PROGRESS, 0L)));
            job.setCurrentStageCompletedTaskCount(Math.toIntExact(counts.getOrDefault(MasterCatalogTaskStatus.COMPLETED, 0L)));
        });

        return new PageImpl<>(batchJobs, pageRequest, batchJobPage.getTotalElements());
    }

    private MasterCatalogTaskType getTaskTypeByJobStatus(MasterCatalogBatchJobStatus status) {
       return switch (status) {
            case REMOVE_DUPLICATION_IN_PROGRESS -> MasterCatalogTaskType.DUPLICATION_IN_BATCH;
            case GENERATE_PRODUCTS_IN_PROGRESS -> MasterCatalogTaskType.DUPLICATION_WITH_PRODUCT;
           default -> null;
        };
    }
}
