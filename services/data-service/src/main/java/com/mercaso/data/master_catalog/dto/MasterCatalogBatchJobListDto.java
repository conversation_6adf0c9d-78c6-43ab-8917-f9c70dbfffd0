package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for {@link MasterCatalogBatchJob}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MasterCatalogBatchJobListDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private MasterCatalogBatchJobStatus status;
    private MasterCatalogTaskType taskType;
    private Integer currentStageTaskCount;
    private Integer currentStagePendingTaskCount;
    private Integer currentStageAssignedTaskCount;
    private Integer currentStageInProcessTaskCount;
    private Integer currentStageCompletedTaskCount;
    private Instant completedAt;
}