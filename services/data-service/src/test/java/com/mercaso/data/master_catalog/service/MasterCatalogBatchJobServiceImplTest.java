package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.event.applicationevent.BusinessApplicationEventType;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventPublisher;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationStartPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository.ProductUpcAndName;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogUpcPoolRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogBatchJobServiceImpl;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

class MasterCatalogBatchJobServiceImplTest {

    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
    private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository = mock(
        MasterCatalogBatchJobRepository.class);
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper = mock(
        MasterCatalogBatchJobMapper.class);
    private final MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository = mock(
        MasterCatalogRawDataDuplicationRepository.class);
    private final ApplicationEventPublisher applicationEventPublisher = mock(ApplicationEventPublisher.class);
    private final MasterCatalogUpcPoolRepository masterCatalogUpcPoolRepository = mock(MasterCatalogUpcPoolRepository.class);
    private final MasterCatalogProductRepository masterCatalogProductRepository = mock(MasterCatalogProductRepository.class);
    private final MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService = mock(
        MasterCatalogRawDataDuplicationService.class);
    private final MasterCatalogTaskRepository masterCatalogTaskRepository = mock(MasterCatalogTaskRepository.class);

    private final MasterCatalogBatchJobService masterCatalogBatchJobService = new MasterCatalogBatchJobServiceImpl(
        masterCatalogRawDataRepository,
        masterCatalogBatchJobRepository,
        masterCatalogBatchJobMapper,
        masterCatalogRawDataDuplicationRepository,
        applicationEventPublisher,
        masterCatalogUpcPoolRepository,
        masterCatalogProductRepository,
        masterCatalogRawDataDuplicationService,
        masterCatalogTaskRepository
    );

    @Test
    void startInitialProcessing_WithInProgressTask_ShouldSkipProcessing() {
        // Given
        when(masterCatalogBatchJobRepository.existsAllByStatusIsNot(
            MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_COMPLETED)).thenReturn(true);

        // When
        masterCatalogBatchJobService.startInitialProcessing(10);

        // Then
        verify(masterCatalogBatchJobRepository, never()).save(any());
        verify(masterCatalogBatchJobMapper, never()).toDto(any());
        verify(masterCatalogRawDataRepository, never()).findAllByStatus(any(), any(Pageable.class));
    }

    @Test
    void startInitialProcessing_WithNoInProgressTask_ShouldCreateTaskAndProcess() {
        // Given
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJob task = new MasterCatalogBatchJob();
        task.setId(taskId);
        task.setStatus(MasterCatalogBatchJobStatus.DRAFT);

        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        // Create raw data records
        UUID rawDataId1 = UUID.randomUUID();
        MasterCatalogRawData rawData1 = new MasterCatalogRawData();
        rawData1.setId(rawDataId1);
        rawData1.setUpc("12345678901234");
        rawData1.setName("Test Product");
        rawData1.setStatus(RawDataStatus.DRAFT.name());

        List<MasterCatalogRawData> rawDataList = Collections.singletonList(rawData1);
        Page<MasterCatalogRawData> page = new PageImpl<>(rawDataList);

        // Mock repository responses
        when(masterCatalogBatchJobRepository.existsAllByStatusIsNot(
            MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_COMPLETED)).thenReturn(false);
        when(masterCatalogBatchJobRepository.save(any())).thenReturn(task);
        when(masterCatalogBatchJobMapper.toDto(task)).thenReturn(taskDto);
        when(masterCatalogRawDataRepository.findAllByStatus(eq(RawDataStatus.DRAFT.name()), any(Pageable.class)))
            .thenReturn(page);
        when(masterCatalogUpcPoolRepository.findAllUpcsNotIn(anyString()))
            .thenReturn(Collections.singletonList("12345678901234"));
        when(masterCatalogProductRepository.findAllUpcsByName(anyList()))
            .thenReturn(Collections.emptyList());

        // When
        masterCatalogBatchJobService.startInitialProcessing(10);

        // Then
        verify(masterCatalogBatchJobRepository).save(any());
        verify(masterCatalogBatchJobMapper).toDto(any());
        verify(masterCatalogRawDataRepository).findAllByStatus(eq(RawDataStatus.DRAFT.name()), any(Pageable.class));
        verify(masterCatalogUpcPoolRepository).saveAll(anyList());

        // Verify event was published
        ArgumentCaptor<RemoveDuplicationStartPayload> payloadCaptor = ArgumentCaptor.forClass(
            RemoveDuplicationStartPayload.class);
        verify(applicationEventPublisher).publish(eq(BusinessApplicationEventType.REMOVE_DUPLICATION_START),
            payloadCaptor.capture());

        RemoveDuplicationStartPayload capturedPayload = payloadCaptor.getValue();
        assertEquals(taskId, capturedPayload.getBatchJobId());
        assertTrue(capturedPayload.getRawDataIds().contains(rawDataId1));
    }

    @Test
    void startInitialProcessing_WithNoRawData_ShouldCreateTaskButStopProcessing() {
        // Given
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJob task = new MasterCatalogBatchJob();
        task.setId(taskId);
        task.setStatus(MasterCatalogBatchJobStatus.DRAFT);

        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        Page<MasterCatalogRawData> emptyPage = new PageImpl<>(Collections.emptyList());

        when(masterCatalogBatchJobRepository.existsAllByStatusIsNot(
            MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_COMPLETED)).thenReturn(false);
        when(masterCatalogBatchJobRepository.save(any())).thenReturn(task);
        when(masterCatalogBatchJobMapper.toDto(task)).thenReturn(taskDto);
        when(masterCatalogRawDataRepository.findAllByStatus(eq(RawDataStatus.DRAFT.name()), any(Pageable.class)))
            .thenReturn(emptyPage);

        // When
        masterCatalogBatchJobService.startInitialProcessing(10);

        // Then
        verify(masterCatalogBatchJobRepository).save(any());
        verify(masterCatalogBatchJobMapper).toDto(any());
        verify(masterCatalogRawDataRepository).findAllByStatus(eq(RawDataStatus.DRAFT.name()), any(Pageable.class));
        verify(applicationEventPublisher, never()).publish(any(), any());
        verify(masterCatalogUpcPoolRepository, never()).saveAll(anyList());
    }

    @Test
    void startInitialProcessing_WithDuplicateNamedProducts_ShouldHandleDuplications() {
        // Given
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJob task = new MasterCatalogBatchJob();
        task.setId(taskId);
        task.setStatus(MasterCatalogBatchJobStatus.DRAFT);

        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        // Create two raw data records with the same name but different UPCs
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();

        MasterCatalogRawData rawData1 = new MasterCatalogRawData();
        rawData1.setId(rawDataId1);
        rawData1.setUpc("111111111111");
        rawData1.setName("Same Product Name");
        rawData1.setStatus(RawDataStatus.DRAFT.name());

        MasterCatalogRawData rawData2 = new MasterCatalogRawData();
        rawData2.setId(rawDataId2);
        rawData2.setUpc("************");
        rawData2.setName("Same Product Name");
        rawData2.setStatus(RawDataStatus.DRAFT.name());

        List<MasterCatalogRawData> rawDataList = Arrays.asList(rawData1, rawData2);
        Page<MasterCatalogRawData> page = new PageImpl<>(rawDataList);

        // Mock repository responses
        when(masterCatalogBatchJobRepository.existsAllByStatusIsNot(
            MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_COMPLETED)).thenReturn(false);
        when(masterCatalogBatchJobRepository.save(any())).thenReturn(task);
        when(masterCatalogBatchJobMapper.toDto(task)).thenReturn(taskDto);
        when(masterCatalogRawDataRepository.findAllByStatus(eq(RawDataStatus.DRAFT.name()), any(Pageable.class)))
            .thenReturn(page);
        when(masterCatalogUpcPoolRepository.findAllUpcsNotIn(anyString()))
            .thenReturn(Arrays.asList(rawData1.getUpc(), rawData2.getUpc()));

        // Mock product repository to simulate existing products with similar names
        ProductUpcAndName productUpcAndName = new ProductUpcAndName() {
            @Override
            public String getName() {
                return "same product name";
            }

            @Override
            public String getUpc() {
                return "98765432101234";
            }
        };
        when(masterCatalogProductRepository.findAllUpcsByName(anyList()))
            .thenReturn(Collections.singletonList(productUpcAndName));

        // Mock duplication repository
        when(masterCatalogRawDataDuplicationRepository.findAllUnderTheSameGroupByUpcIn(anyList()))
            .thenReturn(Collections.emptyList());
        when(masterCatalogRawDataDuplicationService.mergeDuplicationRecords(anyList()))
            .thenReturn(null);

        // When
        masterCatalogBatchJobService.startInitialProcessing(10);

        // Then
        verify(masterCatalogBatchJobRepository).save(any());
        verify(masterCatalogRawDataRepository).findAllByStatus(eq(RawDataStatus.DRAFT.name()), any(Pageable.class));
        verify(masterCatalogUpcPoolRepository).saveAll(anyList());
        verify(masterCatalogRawDataDuplicationRepository).saveAll(anyList());

        // Verify the event was published
        ArgumentCaptor<RemoveDuplicationStartPayload> payloadCaptor = ArgumentCaptor.forClass(
            RemoveDuplicationStartPayload.class);
        verify(applicationEventPublisher).publish(eq(BusinessApplicationEventType.REMOVE_DUPLICATION_START),
            payloadCaptor.capture());

        RemoveDuplicationStartPayload capturedPayload = payloadCaptor.getValue();
        assertEquals(1, capturedPayload.getRawDataIds().size());

        // Verify a raw data ID is present, but don't assume which one
        // The implementation includes the first item in the list after grouping by name
        assertTrue(!capturedPayload.getRawDataIds().isEmpty());

        assertTrue(capturedPayload.getRawDataIds().size() == 1);

        // Verify the second raw data ID is not included (it should be marked as duplicate)
        assertTrue(capturedPayload.getRawDataIds().contains(rawDataId2) || capturedPayload.getRawDataIds().contains(rawDataId1));
    }

    @Test
    void search_WithStatusFilter_ShouldReturnFilteredJobs() {
        // Given
        UUID jobId = UUID.randomUUID();
        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(jobId);
        job.setStatus(MasterCatalogBatchJobStatus.DRAFT);
        
        MasterCatalogBatchJobListDto jobDto = new MasterCatalogBatchJobListDto();
        jobDto.setId(jobId);
        jobDto.setStatus(MasterCatalogBatchJobStatus.DRAFT);
        
        Page<MasterCatalogBatchJob> jobPage = new PageImpl<>(List.of(job));
        PageRequest pageRequest = PageRequest.of(0, 10);
        
        when(masterCatalogBatchJobRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(jobPage);
        when(masterCatalogBatchJobMapper.toListDto(job)).thenReturn(jobDto);
        when(masterCatalogTaskRepository.findByJobIdIn(anyList())).thenReturn(Collections.emptyList());

        // When
        Page<MasterCatalogBatchJobListDto> result = masterCatalogBatchJobService.search(MasterCatalogBatchJobStatus.DRAFT, pageRequest);

        // Then
        assertEquals(1, result.getContent().size());
        assertEquals(MasterCatalogBatchJobStatus.DRAFT, result.getContent().get(0).getStatus());
        verify(masterCatalogBatchJobRepository).findAll(any(Specification.class), eq(pageRequest));
    }

    @Test
    void search_WithNullStatus_ShouldReturnAllJobs() {
        // Given
        UUID jobId = UUID.randomUUID();
        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);
        job.setId(jobId);
        
        MasterCatalogBatchJobListDto jobDto = new MasterCatalogBatchJobListDto();
        jobDto.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);
        jobDto.setId(jobId);
        
        Page<MasterCatalogBatchJob> jobPage = new PageImpl<>(List.of(job));
        PageRequest pageRequest = PageRequest.of(0, 10);
        
        when(masterCatalogBatchJobRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(jobPage);
        when(masterCatalogBatchJobMapper.toListDto(job)).thenReturn(jobDto);
        when(masterCatalogTaskRepository.findByJobIdIn(anyList())).thenReturn(Collections.emptyList());

        // When
        Page<MasterCatalogBatchJobListDto> result = masterCatalogBatchJobService.search(null, pageRequest);

        // Then
        assertEquals(1, result.getContent().size());
        verify(masterCatalogBatchJobRepository).findAll(any(Specification.class), eq(pageRequest));
    }

    @Test
    void search_WithTaskCounts_ShouldCalculateTaskStatistics() {
        // Given
        UUID jobId = UUID.randomUUID();
        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);
        job.setId(jobId);
        
        MasterCatalogBatchJobListDto jobDto = new MasterCatalogBatchJobListDto();
        jobDto.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);
        jobDto.setId(jobId);
        
        MasterCatalogTask pendingTask = new MasterCatalogTask();
        pendingTask.setJobId(jobId);
        pendingTask.setType(MasterCatalogTaskType.DUPLICATION_IN_BATCH);
        pendingTask.setStatus(MasterCatalogTaskStatus.PENDING);
        
        MasterCatalogTask assignedTask = new MasterCatalogTask();
        assignedTask.setJobId(jobId);
        assignedTask.setType(MasterCatalogTaskType.DUPLICATION_IN_BATCH);
        assignedTask.setStatus(MasterCatalogTaskStatus.ASSIGNED);
        
        Page<MasterCatalogBatchJob> jobPage = new PageImpl<>(List.of(job));
        PageRequest pageRequest = PageRequest.of(0, 10);
        
        when(masterCatalogBatchJobRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(jobPage);
        when(masterCatalogBatchJobMapper.toListDto(job)).thenReturn(jobDto);
        when(masterCatalogTaskRepository.findByJobIdIn(List.of(jobId))).thenReturn(List.of(pendingTask, assignedTask));

        // When
        Page<MasterCatalogBatchJobListDto> result = masterCatalogBatchJobService.search(null, pageRequest);

        // Then
        MasterCatalogBatchJobListDto resultDto = result.getContent().get(0);
        assertEquals(MasterCatalogTaskType.DUPLICATION_IN_BATCH, resultDto.getTaskType());
        assertEquals(2, resultDto.getCurrentStageTaskCount());
        assertEquals(1, resultDto.getCurrentStagePendingTaskCount());
        assertEquals(1, resultDto.getCurrentStageAssignedTaskCount());
        assertEquals(0, resultDto.getCurrentStageInProcessTaskCount());
        assertEquals(0, resultDto.getCurrentStageCompletedTaskCount());
    }
}
