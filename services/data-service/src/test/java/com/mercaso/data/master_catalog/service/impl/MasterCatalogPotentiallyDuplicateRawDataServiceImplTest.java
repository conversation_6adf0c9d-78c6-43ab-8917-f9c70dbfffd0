package com.mercaso.data.master_catalog.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateRawDataRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateSubmitRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyDuplicateRawDataMapper;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogImageService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.hibernate.ObjectNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

class MasterCatalogPotentiallyDuplicateRawDataServiceImplTest {

    private MasterCatalogPotentiallyDuplicateRawDataRepository duplicateRawDataRepository;
    private MasterCatalogBatchJobRepository jobRepository;
    private MasterCatalogPotentiallyDuplicateRawDataMapper duplicateRawDataMapper;
    private MasterCatalogRawDataService masterCatalogRawDataService;
    private ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private MasterCatalogBatchJobMapper jobMapper;
    private MasterCatalogTaskRepository masterCatalogTaskRepository;
    private MasterCatalogTaskService masterCatalogTaskService;
    private MasterCatalogPotentiallyDuplicateRawDataServiceImpl service;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private MasterCatalogImageService masterCatalogImageService;

    @BeforeEach
    void setUp() {
        duplicateRawDataRepository = mock(MasterCatalogPotentiallyDuplicateRawDataRepository.class);
        jobRepository = mock(MasterCatalogBatchJobRepository.class);
        duplicateRawDataMapper = mock(MasterCatalogPotentiallyDuplicateRawDataMapper.class);
        masterCatalogRawDataService = mock(MasterCatalogRawDataService.class);
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);
        jobMapper = mock(MasterCatalogBatchJobMapper.class);
        masterCatalogTaskService = mock(MasterCatalogTaskService.class);
        masterCatalogTaskRepository = mock(MasterCatalogTaskRepository.class);
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        masterCatalogImageService = mock(MasterCatalogImageService.class);
        service = new MasterCatalogPotentiallyDuplicateRawDataServiceImpl(
            duplicateRawDataRepository,
            jobRepository,
            masterCatalogTaskService,
            masterCatalogTaskRepository,
            duplicateRawDataMapper,
            masterCatalogRawDataService,
            applicationEventPublisherProvider,
            masterCatalogRawDataRepository,
            masterCatalogImageService
        );
        service.setTASK_RECORD_COUNT(20);
    }

    @Test
    void list_WithEmptyStatus_ShouldReturnPendingData() {
        // Arrange
        PotentiallyDuplicateRawDataRequest request = new PotentiallyDuplicateRawDataRequest();
        request.setPage(1);
        request.setPageSize(10);

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(UUID.randomUUID());
        List<MasterCatalogPotentiallyDuplicateRawData> rawDataList = List.of(new MasterCatalogPotentiallyDuplicateRawData());
        Page<MasterCatalogPotentiallyDuplicateRawData> rawDataPage = new PageImpl<>(rawDataList);

        when(jobRepository.findAllByStatusIn(any())).thenReturn(List.of(job));
        when(duplicateRawDataRepository.findAll(any(Specification.class), any(PageRequest.class))).thenReturn(rawDataPage);
        when(duplicateRawDataMapper.toDto(any())).thenReturn(new MasterCatalogPotentiallyDuplicateRawDataDto());
        when(masterCatalogImageService.getImagesPathByRawDataIds(any())).thenReturn(Collections.emptyMap());

        // Act
        Page<MasterCatalogPotentiallyDuplicateRawDataDto> result = service.list(request);

        // Assert
        assertThat(result).hasSize(1);
        verify(duplicateRawDataRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void list_WithSpecificStatus_ShouldReturnFilteredData() {
        // Arrange
        PotentiallyDuplicateRawDataRequest request = new PotentiallyDuplicateRawDataRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setStatus(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW.name());

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(UUID.randomUUID());
        List<MasterCatalogPotentiallyDuplicateRawData> rawDataList = List.of(new MasterCatalogPotentiallyDuplicateRawData());
        Page<MasterCatalogPotentiallyDuplicateRawData> rawDataPage = new PageImpl<>(rawDataList);

        when(jobRepository.findAllByStatusIn(any())).thenReturn(List.of(job));
        when(duplicateRawDataRepository.findAll(any(Specification.class), any(PageRequest.class))).thenReturn(rawDataPage);
        when(duplicateRawDataMapper.toDto(any())).thenReturn(new MasterCatalogPotentiallyDuplicateRawDataDto());
        when(masterCatalogImageService.getImagesPathByRawDataIds(any())).thenReturn(Collections.emptyMap());

        // Act
        Page<MasterCatalogPotentiallyDuplicateRawDataDto> result = service.list(request);

        // Assert
        assertThat(result).hasSize(1);
        verify(duplicateRawDataRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void list_WithJobIdFilter_ShouldReturnFilteredData() {
        // Arrange
        PotentiallyDuplicateRawDataRequest request = new PotentiallyDuplicateRawDataRequest();
        request.setPage(1);
        request.setPageSize(10);

        List<MasterCatalogPotentiallyDuplicateRawData> rawDataList = List.of(new MasterCatalogPotentiallyDuplicateRawData());
        Page<MasterCatalogPotentiallyDuplicateRawData> rawDataPage = new PageImpl<>(rawDataList);

        when(duplicateRawDataRepository.findAll(any(Specification.class), any(PageRequest.class))).thenReturn(rawDataPage);
        when(duplicateRawDataMapper.toDto(any())).thenReturn(new MasterCatalogPotentiallyDuplicateRawDataDto());
        when(masterCatalogImageService.getImagesPathByRawDataIds(any())).thenReturn(Collections.emptyMap());

        // Act
        Page<MasterCatalogPotentiallyDuplicateRawDataDto> result = service.list(request);

        // Assert
        assertThat(result).hasSize(1);
        verify(duplicateRawDataRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void list_WithTaskIdFilter_ShouldReturnFilteredData() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        PotentiallyDuplicateRawDataRequest request = new PotentiallyDuplicateRawDataRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setTaskId(taskId.toString());

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(UUID.randomUUID());
        List<MasterCatalogPotentiallyDuplicateRawData> rawDataList = List.of(new MasterCatalogPotentiallyDuplicateRawData());
        Page<MasterCatalogPotentiallyDuplicateRawData> rawDataPage = new PageImpl<>(rawDataList);

        when(jobRepository.findAllByStatusIn(any())).thenReturn(List.of(job));
        when(duplicateRawDataRepository.findAll(any(Specification.class), any(PageRequest.class))).thenReturn(rawDataPage);
        when(duplicateRawDataMapper.toDto(any())).thenReturn(new MasterCatalogPotentiallyDuplicateRawDataDto());
        when(masterCatalogImageService.getImagesPathByRawDataIds(any())).thenReturn(Collections.emptyMap());

        // Act
        Page<MasterCatalogPotentiallyDuplicateRawDataDto> result = service.list(request);

        // Assert
        assertThat(result).hasSize(1);
        verify(duplicateRawDataRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void list_WithPagination_ShouldRespectPageSize() {
        // Arrange
        PotentiallyDuplicateRawDataRequest request = new PotentiallyDuplicateRawDataRequest();
        request.setPage(2);
        request.setPageSize(5);

        MasterCatalogBatchJob task = new MasterCatalogBatchJob();
        List<MasterCatalogPotentiallyDuplicateRawData> rawDataList = List.of(new MasterCatalogPotentiallyDuplicateRawData());
        PageRequest pageRequest = PageRequest.of(1, 5);
        Page<MasterCatalogPotentiallyDuplicateRawData> rawDataPage = new PageImpl<>(rawDataList, pageRequest, 10);

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(UUID.randomUUID());
        when(jobRepository.findAllByStatusIn(any())).thenReturn(List.of(job));
        when(duplicateRawDataRepository.findAll(any(Specification.class), any(PageRequest.class))).thenReturn(rawDataPage);
        when(masterCatalogImageService.getImagesPathByRawDataIds(any())).thenReturn(Collections.emptyMap());
        when(duplicateRawDataMapper.toDto(any())).thenReturn(new MasterCatalogPotentiallyDuplicateRawDataDto());

        // Act
        Page<MasterCatalogPotentiallyDuplicateRawDataDto> result = service.list(request);

        // Assert
        assertThat(result.getSize()).isEqualTo(5);
        assertThat(result.getNumber()).isEqualTo(1);
        assertThat(result.getTotalElements()).isEqualTo(10);
    }

    @Test
    void submit_WithEmptyIds_ShouldDoNothing() {
        // Act
        PotentiallyDuplicateSubmitRequest request = new PotentiallyDuplicateSubmitRequest();
        request.setPotentiallyDuplicateRawDataIds(new ArrayList<>());
        service.submit(request);

        // Assert
        verify(duplicateRawDataRepository, never()).saveAll(anyList());
        verify(applicationEventPublisherProvider, never()).removeDuplicationSubmitEvent(any(), any());
    }

    @Test
    void submit_WithNoValidData_ShouldNotUpdateOrPublish() {
        // Arrange
        UUID id1 = UUID.randomUUID();

        PotentiallyDuplicateSubmitRequest request = new PotentiallyDuplicateSubmitRequest();
        request.setPotentiallyDuplicateRawDataIds(List.of(id1));

        // Act
        service.submit(request);

        // Assert
        verify(duplicateRawDataRepository, never()).saveAll(any());
        verify(applicationEventPublisherProvider, never()).removeDuplicationSubmitEvent(any(), any());
    }

    @Test
    void submit_WithValidData_ShouldUpdateStatusAndPublishEvent() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        PotentiallyDuplicateSubmitRequest request = new PotentiallyDuplicateSubmitRequest();
        request.setPotentiallyDuplicateRawDataIds(List.of(id1, id2));


        MasterCatalogPotentiallyDuplicateRawData data1 = new MasterCatalogPotentiallyDuplicateRawData();
        data1.setId(id1);
        data1.setJobId(jobId);
        data1.setTaskId(taskId);
        data1.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);

        MasterCatalogPotentiallyDuplicateRawData data2 = new MasterCatalogPotentiallyDuplicateRawData();
        data2.setId(id2);
        data2.setTaskId(taskId);
        data2.setJobId(jobId);
        data2.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);

        MasterCatalogTask task = new MasterCatalogTask();
        task.setId(taskId);
        task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(jobId);
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);

        when(duplicateRawDataRepository.findAllById(anyList())).thenReturn(List.of(data1, data2));
        when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));
        when(duplicateRawDataRepository.findByTaskId(any())).thenReturn(List.of(data1,data2));
        when(jobRepository.findById(jobId)).thenReturn(Optional.of(job));

        // Act
        service.submit(request);

        // Assert
        ArgumentCaptor<List<MasterCatalogPotentiallyDuplicateRawData>> dataCaptor = ArgumentCaptor.forClass(List.class);
        verify(duplicateRawDataRepository).saveAll(dataCaptor.capture());

        List<MasterCatalogPotentiallyDuplicateRawData> savedData = dataCaptor.getValue();
        assertThat(savedData).hasSize(2);
        assertThat(savedData.get(0).getStatus()).isEqualTo(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
        assertThat(savedData.get(1).getStatus()).isEqualTo(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);

        verify(applicationEventPublisherProvider).removeDuplicationSubmitEvent(any(), any());
    }

    @Test
    void update_WithValidData_ShouldUpdateDuplicatedFlag() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        request.setDuplicated(true);

        MasterCatalogPotentiallyDuplicateRawData data = new MasterCatalogPotentiallyDuplicateRawData();
        data.setId(id);
        data.setTaskId(taskId);
        data.setJobId(jobId);
        data.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
        data.setDuplicated(false);

        MasterCatalogTask task = new MasterCatalogTask();
        task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);
        task.setId(taskId);

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(jobId);
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);

        MasterCatalogPotentiallyDuplicateRawDataDto expectedDto = new MasterCatalogPotentiallyDuplicateRawDataDto();
        expectedDto.setId(id);
        expectedDto.setDuplicated(true);

        when(duplicateRawDataRepository.findById(id)).thenReturn(Optional.of(data));
        when(jobRepository.findById(jobId)).thenReturn(Optional.of(job));
        when(duplicateRawDataRepository.save(any())).thenReturn(data);
        when(duplicateRawDataMapper.toDto(data)).thenReturn(expectedDto);
        when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));

        // Act
        MasterCatalogPotentiallyDuplicateRawDataDto result = service.update(id, request);

        // Assert
        assertThat(result).isEqualTo(expectedDto);
        assertThat(data.getDuplicated()).isTrue();
        verify(duplicateRawDataRepository).save(data);
    }

    @Test
    void update_WithNonExistentData_ShouldThrowObjectNotFoundException() {
        // Arrange
        UUID id = UUID.randomUUID();
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        request.setDuplicated(true);

        when(duplicateRawDataRepository.findById(id)).thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> service.update(id, request))
            .isInstanceOf(ObjectNotFoundException.class)
            .hasMessageContaining(id.toString());
    }

    @Test
    void update_WithAlreadyReviewedData_ShouldThrowIllegalArgumentException() {
        // Arrange
        UUID id = UUID.randomUUID();
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        request.setDuplicated(true);

        MasterCatalogPotentiallyDuplicateRawData data = new MasterCatalogPotentiallyDuplicateRawData();
        data.setId(id);
        data.setStatus(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED); // Already reviewed

        when(duplicateRawDataRepository.findById(id)).thenReturn(Optional.of(data));

        // Act & Assert
        assertThatThrownBy(() -> service.update(id, request))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("already reviewed");
    }

    @Test
    void update_WithInvalidTaskStatus_ShouldThrowIllegalArgumentException() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        request.setDuplicated(true);

        MasterCatalogPotentiallyDuplicateRawData data = new MasterCatalogPotentiallyDuplicateRawData();
        data.setId(id);
        data.setJobId(taskId);
        data.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);

        MasterCatalogBatchJob task = new MasterCatalogBatchJob();
        task.setId(taskId);
        task.setStatus(MasterCatalogBatchJobStatus.DRAFT); // Invalid status for update

        when(duplicateRawDataRepository.findById(id)).thenReturn(Optional.of(data));
        when(jobRepository.findById(taskId)).thenReturn(Optional.of(task));

        // Act & Assert
        assertThatThrownBy(() -> service.update(id, request))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("job is not in available submit job status");
    }

    @Test
    void update_WithTaskNotFound_ShouldThrowIllegalArgumentException() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        request.setDuplicated(true);

        MasterCatalogPotentiallyDuplicateRawData data = new MasterCatalogPotentiallyDuplicateRawData();
        data.setId(id);
        data.setJobId(taskId);
        data.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);

        when(duplicateRawDataRepository.findById(id)).thenReturn(Optional.of(data));
        when(jobRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> service.update(id, request))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("job is not in available submit job status");
    }

    @Test
    void update_WithNullDuplicatedFlag_ShouldNotUpdateFlag() {
        // Arrange
        UUID id = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        request.setDuplicated(null); // Null flag

        MasterCatalogPotentiallyDuplicateRawData data = new MasterCatalogPotentiallyDuplicateRawData();
        data.setId(id);
        data.setJobId(jobId);
        data.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
        data.setDuplicated(false); // Original value

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(jobId);
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);

        MasterCatalogTaskDto taskDto = new MasterCatalogTaskDto();
        taskDto.setId(taskId);
        taskDto.setType(MasterCatalogTaskType.DUPLICATION_IN_BATCH);
        taskDto.setJobId(jobId);
        taskDto.setStatus(MasterCatalogTaskStatus.PENDING);

        MasterCatalogPotentiallyDuplicateRawDataDto expectedDto = new MasterCatalogPotentiallyDuplicateRawDataDto();
        expectedDto.setId(id);
        expectedDto.setDuplicated(false); // Should remain unchanged

        when(duplicateRawDataRepository.findById(id)).thenReturn(Optional.of(data));
        when(jobRepository.findById(jobId)).thenReturn(Optional.of(job));
        when(duplicateRawDataRepository.save(any())).thenReturn(data);
        when(duplicateRawDataMapper.toDto(data)).thenReturn(expectedDto);
        when(masterCatalogTaskService.createTasks(any(),any(),any())).thenReturn(List.of(taskDto));

        // Act
        MasterCatalogPotentiallyDuplicateRawDataDto result = service.update(id, request);

        // Assert
        assertThat(result).isEqualTo(expectedDto);
        assertThat(data.getDuplicated()).isFalse(); // Should remain unchanged
    }

    @Test
    public void testGeneratePotentialDuplication() {
        UUID jobId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        MasterCatalogTaskType type = MasterCatalogTaskType.DUPLICATION_IN_BATCH;
        PotentiallyDuplicateRawDataStatus status = PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED;

        MasterCatalogRawData rawData1 = new MasterCatalogRawData();
        rawData1.setId(rawDataId1);
        rawData1.setName("Product A");
        rawData1.setUpc("1111111111");
        rawData1.setPrimaryVendor("Vendor XYZ");
        rawData1.setCreatedAt(Instant.now());
        rawData1.setUpdatedAt(Instant.now());
        MasterCatalogRawData rawData2 = new MasterCatalogRawData();
        rawData2.setId(rawDataId2);
        rawData2.setName("Product B");
        rawData2.setUpc("1111111111");
        rawData2.setPrimaryVendor("Vendor XYZ");
        rawData2.setCreatedAt(Instant.now());
        rawData2.setUpdatedAt(Instant.now());

        MasterCatalogPotentiallyDuplicateRawData masterCatalogPotentiallyDuplicateRawData1 = new MasterCatalogPotentiallyDuplicateRawData();
        masterCatalogPotentiallyDuplicateRawData1.setRawDataId(rawDataId1);
        masterCatalogPotentiallyDuplicateRawData1.setName("Product A");
        masterCatalogPotentiallyDuplicateRawData1.setUpc("1111111111");
        masterCatalogPotentiallyDuplicateRawData1.setPrimaryVendor("Vendor XYZ");
        masterCatalogPotentiallyDuplicateRawData1.setStatus(
            PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);

        MasterCatalogPotentiallyDuplicateRawData masterCatalogPotentiallyDuplicateRawData2 = new MasterCatalogPotentiallyDuplicateRawData();
        masterCatalogPotentiallyDuplicateRawData2.setRawDataId(rawDataId1);
        masterCatalogPotentiallyDuplicateRawData2.setName("Product A");
        masterCatalogPotentiallyDuplicateRawData2.setUpc("1111111111");
        masterCatalogPotentiallyDuplicateRawData2.setPrimaryVendor("Vendor XYZ");
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateRawDataId(rawDataId2);
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateName("Product B");
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateUpc("1111111111");
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateVendor("Vendor XYZ");
        masterCatalogPotentiallyDuplicateRawData2.setStatus(
            PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);
        when(masterCatalogRawDataRepository.findById(rawDataId1)).thenReturn(Optional.of(rawData1));
        when(masterCatalogRawDataRepository.findById(rawDataId2)).thenReturn(Optional.of(rawData1));

        when(masterCatalogRawDataRepository.findAllById((List.of(rawDataId1)))).thenReturn(
            List.of(rawData1));
        when(masterCatalogRawDataRepository.findAllById(
            (Arrays.asList(rawDataId1, rawDataId2)))).thenReturn(
            Arrays.asList(rawData1, rawData2));
        when(
            duplicateRawDataMapper.mapRawDataToPotentiallyUnDuplicateRawData(
                any(UUID.class), any(MasterCatalogRawData.class), eq(jobId),
                eq(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED))).thenReturn(
            masterCatalogPotentiallyDuplicateRawData1);

        when(duplicateRawDataMapper.mapRawDataToPotentialDuplicateRawData(
            any(UUID.class), any(UUID.class), any(MasterCatalogRawData.class),
            any(MasterCatalogRawData.class),
            eq(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW))).thenReturn(
            masterCatalogPotentiallyDuplicateRawData2);
        List<List<UUID>> rawDataIds = Arrays.asList(Arrays.asList(rawDataId1, rawDataId2),
            Collections.singletonList(rawDataId1));

        MasterCatalogTaskDto taskDto = new MasterCatalogTaskDto();
        taskDto.setId(taskId);
        taskDto.setType(MasterCatalogTaskType.DUPLICATION_IN_BATCH);
        taskDto.setJobId(jobId);
        taskDto.setStatus(MasterCatalogTaskStatus.PENDING);

        when(masterCatalogTaskService.createTasks(any(),any(),any())).thenReturn(List.of(taskDto));

        service.processPotentiallyDuplicateItem(rawDataIds,
            jobId, status, type);

        ArgumentCaptor<List<MasterCatalogPotentiallyDuplicateRawData>> captor =
            ArgumentCaptor.forClass(List.class);

        verify(duplicateRawDataRepository, times(1)).saveAll(
            captor.capture());

        List<MasterCatalogPotentiallyDuplicateRawData> processedData = captor.getAllValues()
            .getFirst();

        assertNotNull(processedData);
        assertEquals(2, processedData.size());

        MasterCatalogPotentiallyDuplicateRawData unduplicateProcessedData = processedData.get(0);
        assertEquals(unduplicateProcessedData.getStatus(),
            PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);

        MasterCatalogPotentiallyDuplicateRawData duplicateProcessedData = processedData.get(1);
        assertEquals(duplicateProcessedData.getStatus(),
            PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);
    }

    @Test
    public void testProcessPotentiallyDuplicateItemRawDataNotFound() {
        UUID jobId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        MasterCatalogTaskType type = MasterCatalogTaskType.DUPLICATION_IN_BATCH;
        PotentiallyDuplicateRawDataStatus status = PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED;

        MasterCatalogRawData rawData1 = new MasterCatalogRawData();
        rawData1.setId(rawDataId1);
        rawData1.setName("Product A");
        rawData1.setUpc("1111111111");
        rawData1.setPrimaryVendor("Vendor XYZ");
        rawData1.setCreatedAt(Instant.now());
        rawData1.setUpdatedAt(Instant.now());
        MasterCatalogRawData rawData2 = new MasterCatalogRawData();
        rawData2.setId(rawDataId2);
        rawData2.setName("Product B");
        rawData2.setUpc("1111111111");
        rawData2.setPrimaryVendor("Vendor XYZ");
        rawData2.setCreatedAt(Instant.now());
        rawData2.setUpdatedAt(Instant.now());

        MasterCatalogPotentiallyDuplicateRawData masterCatalogPotentiallyDuplicateRawData1 = new MasterCatalogPotentiallyDuplicateRawData();
        masterCatalogPotentiallyDuplicateRawData1.setRawDataId(rawDataId1);
        masterCatalogPotentiallyDuplicateRawData1.setName("Product A");
        masterCatalogPotentiallyDuplicateRawData1.setUpc("1111111111");
        masterCatalogPotentiallyDuplicateRawData1.setPrimaryVendor("Vendor XYZ");
        masterCatalogPotentiallyDuplicateRawData1.setStatus(
            PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);

        MasterCatalogPotentiallyDuplicateRawData masterCatalogPotentiallyDuplicateRawData2 = new MasterCatalogPotentiallyDuplicateRawData();
        masterCatalogPotentiallyDuplicateRawData2.setRawDataId(rawDataId1);
        masterCatalogPotentiallyDuplicateRawData2.setName("Product A");
        masterCatalogPotentiallyDuplicateRawData2.setUpc("1111111111");
        masterCatalogPotentiallyDuplicateRawData2.setPrimaryVendor("Vendor XYZ");
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateRawDataId(rawDataId2);
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateName("Product B");
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateUpc("1111111111");
        masterCatalogPotentiallyDuplicateRawData2.setPotentiallyDuplicateVendor("Vendor XYZ");
        masterCatalogPotentiallyDuplicateRawData2.setStatus(
            PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);

        MasterCatalogTaskDto taskDto = new MasterCatalogTaskDto();
        taskDto.setId(taskId);
        taskDto.setType(MasterCatalogTaskType.DUPLICATION_IN_BATCH);
        taskDto.setJobId(jobId);
        taskDto.setStatus(MasterCatalogTaskStatus.PENDING);

        when(masterCatalogRawDataRepository.findAllById(anyList())).thenReturn(
            Collections.emptyList());
        when(masterCatalogTaskService.createTasks(any(),any(),any())).thenReturn(List.of(taskDto));
        List<List<UUID>> rawDataIds = Arrays.asList(Arrays.asList(rawDataId1, rawDataId2),
            Collections.singletonList(rawDataId1));

        service.processPotentiallyDuplicateItem(rawDataIds,
            jobId, status, type);

        ArgumentCaptor<List<MasterCatalogPotentiallyDuplicateRawData>> captor =
            ArgumentCaptor.forClass(List.class);

        verify(duplicateRawDataRepository, times(1)).saveAll(
            captor.capture());

        List<MasterCatalogPotentiallyDuplicateRawData> processedData = captor.getAllValues()
            .getFirst();
        assertTrue(processedData.isEmpty());
    }
}