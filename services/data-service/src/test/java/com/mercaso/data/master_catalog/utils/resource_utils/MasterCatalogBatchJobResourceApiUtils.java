package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.HashMap;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class MasterCatalogBatchJobResourceApiUtils extends IntegrationTestRestUtil {

    private static final String V1_BATCH_JOBS_START = "/master-catalog/v1/batch-jobs/start";
    private static final String V1_BATCH_JOBS_SEARCH = "/master-catalog/v1/batch-jobs/search";

    public MasterCatalogBatchJobResourceApiUtils(Environment environment) {
        super(environment);
    }

    public ResponseEntity<Void> startProcessing() {
        return postEntity(V1_BATCH_JOBS_START, null, Void.class);
    }

    public CustomPage<MasterCatalogBatchJobListDto> search(MasterCatalogBatchJobStatus status, Integer page, Integer pageSize) {
        Map<String, String> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));
        
        if (status != null) {
            params.put("status", status.name());
        }

        ParameterizedTypeReference<CustomPage<MasterCatalogBatchJobListDto>> responseType = new ParameterizedTypeReference<>() {};
        return getEntityByMap(V1_BATCH_JOBS_SEARCH, responseType, params).getBody();
    }
}
