package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class Vendor extends BaseDomain {

    private final UUID id;

    private String vendorName;

    private String vendorContactName;

    private String vendorContactTel;

    private String vendorCompanyName;

    private VendorStatus vendorStatus;

    private Boolean externalPicking;

    private String finaleId;

    private Boolean shutdownWindowEnabled;

    private LocalTime shutdownWindowStart;

    private LocalTime shutdownWindowEnd;

    private String shutdownWindowDays;

    public Vendor create(String vendorName, String vendorContactName, String vendorContactTel, String vendorCompanyName) {
        return Vendor.builder()
            .vendorName(vendorName)
            .vendorContactName(vendorContactName)
            .vendorContactTel(vendorContactTel)
            .vendorCompanyName(vendorCompanyName)
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
    }

    public void updateVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public void setShutdownWindow(Boolean shutdownWindowEnabled) {
        this.shutdownWindowEnabled = Boolean.TRUE.equals(shutdownWindowEnabled);
        this.shutdownWindowStart = LocalTime.of(5, 0);
        this.shutdownWindowEnd = LocalTime.of(6, 0);
        this.shutdownWindowDays = "FRIDAY,SATURDAY";
    }

    public void setexternalPicking(Boolean externalPicking) {
        this.externalPicking = externalPicking;
    }

    public void updateVendorContactInfo(String vendorContactName, String vendorContactTel) {
        this.vendorContactName = vendorContactName;
        this.vendorContactTel = vendorContactTel;
    }

    public void updateStatus(VendorStatus vendorStatus) {
        this.vendorStatus = vendorStatus;
    }

    public Boolean judgePrimaryVendor(UUID primaryVendorId) {
        return this.id.equals(primaryVendorId);

    }

    public void updateFinaleId(String finaleId) {
        this.finaleId = finaleId;
    }

    public void updateShutdownWindow(Boolean enabled, LocalTime start, LocalTime end) {
        this.shutdownWindowEnabled = enabled;
        this.shutdownWindowStart = start;
        this.shutdownWindowEnd = end;
    }

    /**
     * Check if current time is within the vendor's shutdown window For example: if shutdownWindowDays = "FRIDAY,SATURDAY" and
     * time is 05:00-06:00, it will return true from Friday 5 AM to Saturday 6 AM
     *
     * @return true if current time is within shutdown window, false otherwise
     */
    public boolean isWithinShutdownWindow() {
        if (!Boolean.TRUE.equals(this.shutdownWindowEnabled)) {
            return false;
        }

        if (this.shutdownWindowStart == null || this.shutdownWindowEnd == null) {
            return false;
        }

        if (this.shutdownWindowDays == null || this.shutdownWindowDays.isEmpty()) {
            return false;
        }

        ZoneId losAngelesZone = ZoneId.of("America/Los_Angeles");
        ZonedDateTime currentDateTime = ZonedDateTime.now(losAngelesZone);
        LocalTime currentTime = currentDateTime.toLocalTime();
        LocalTime startTime = this.shutdownWindowStart;
        LocalTime endTime = this.shutdownWindowEnd;
        String[] allowedDays = this.shutdownWindowDays.split(",");

        String currentDay = currentDateTime.getDayOfWeek().name();

        // Check if current day is in the allowed days
        boolean isCurrentDayAllowed = false;
        for (String day : allowedDays) {
            if (day.trim().equalsIgnoreCase(currentDay)) {
                isCurrentDayAllowed = true;
                break;
            }
        }

        if (!isCurrentDayAllowed) {
            return false;
        }

        if (currentDay.equalsIgnoreCase("FRIDAY")) {
            // Friday: from start time onwards
            return currentTime.isAfter(startTime) || currentTime.equals(startTime);
        } else if (currentDay.equalsIgnoreCase("SATURDAY")) {
            // Sunday: until end time
            return currentTime.isBefore(endTime) || currentTime.equals(endTime);
        }

        return false;
    }
}
