package com.mercaso.ims.infrastructure.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mercaso.ims.infrastructure.excel.data.CoremarkDailyUpdatedData;
import java.util.List;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CoremarkItemDailyUpdatedDataListener implements ReadListener<CoremarkDailyUpdatedData> {

    private final List<CoremarkDailyUpdatedData> coremarkDailyUpdatedDataList;

    @Override
    public void invoke(CoremarkDailyUpdatedData data, AnalysisContext context) {
        coremarkDailyUpdatedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // No additional processing needed after analysis
    }
}
