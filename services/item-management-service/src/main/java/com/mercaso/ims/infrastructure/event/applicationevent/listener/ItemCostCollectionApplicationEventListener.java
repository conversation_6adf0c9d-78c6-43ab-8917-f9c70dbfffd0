package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static com.mercaso.ims.domain.vendor.VendorConstant.COREMARK;
import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;
import static com.mercaso.ims.domain.vendor.VendorConstant.EXOTIC_BLVD;
import static com.mercaso.ims.domain.vendor.VendorConstant.SEVEN_STAR;
import static com.mercaso.ims.domain.vendor.VendorConstant.VERNON_SALES;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;

import com.mercaso.ims.application.command.CreateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.VendorItemMappingDto;
import com.mercaso.ims.application.dto.event.ItemCostCollectionCreatedEvent;
import com.mercaso.ims.application.dto.payload.ItemCostCollectionCreatedPayloadDto;
import com.mercaso.ims.application.service.ItemCostChangeRequestApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.process.matcher.VendorItemMatcher;
import com.mercaso.ims.infrastructure.process.parser.ItemCostCollectionParser;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class ItemCostCollectionApplicationEventListener {

    private final List<ItemCostCollectionParser> itemCostCollectionParsers;
    private final List<VendorItemMatcher> vendorItemMatchers;
    private final ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService;
    private final ItemService itemService;
    private final VendorItemService vendorItemService;
    private final VendorService vendorService;
    private final VendorItemApplicationService vendorItemApplicationService;
    private final ItemRegPriceService itemRegPriceService;
    public static final List<String> COST_NOT_INCLUDE_CRV_SUPPLIERS = List.of(
        DOWNEY_WHOLESALE, VERNON_SALES, SEVEN_STAR, EXOTIC_BLVD, COREMARK
    );

    @EventListener
    public void handleItemCostCollectionCreatedEvent(ItemCostCollectionCreatedEvent itemCostCollectionCreatedEvent) {

        ItemCostCollectionCreatedPayloadDto payload = itemCostCollectionCreatedEvent.getPayload();

        log.info("[handleItemCostCollectionCreatedEvent] for request ={}", payload);

        ItemCostCollectionDto itemCostCollectionDto = payload.getData();

        String vendorName = itemCostCollectionDto.getVendorName();
        ItemCostCollectionParser collectionParser = itemCostCollectionParsers.stream()
            .filter(parser -> parser.isSupported(vendorName, itemCostCollectionDto.getSource()))
            .findFirst()
            .orElseThrow(() -> new ImsBusinessException("Unsupported vendor name: " + itemCostCollectionDto.getVendorName()));

        VendorItemMatcher vendorItemMatcher = vendorItemMatchers.stream()
            .filter(matcher -> matcher.isSupported(vendorName, itemCostCollectionDto.getSource()))
            .findFirst()
            .orElseThrow(() -> new ImsBusinessException("Unsupported vendor name: " + vendorName));

        List<ItemCostCollectionItemParsingResultDto> itemCostCollectionItemParsingResultDtos = collectionParser.parse(
            itemCostCollectionDto.getId());

        if (CollectionUtils.isEmpty(itemCostCollectionItemParsingResultDtos)) {
            log.warn("[handleItemCostCollectionCreatedEvent] No items found for itemCostCollectionId = {}",
                itemCostCollectionDto.getId());
            return;
        }
        List<CreateItemCostChangeRequestCommand> commands = itemCostCollectionItemParsingResultDtos.stream()
            .flatMap(invoiceItem -> vendorItemMatcher.matchItem(invoiceItem, itemCostCollectionDto.getVendorId()).stream()
                .map(itemMapping -> convertToCreateItemCostChangeRequestCommand(
                    invoiceItem,
                    itemMapping,
                    itemCostCollectionDto.getId())
                )
            ).toList();
        log.info("[handleItemCostCollectionCreatedEvent] commands size:{}", commands.size());

        List<CreateItemCostChangeRequestCommand> createItemCostChangeRequestCommands = commands.stream()
            .collect(Collectors.toMap(
                command -> command.getItemId() != null ? command.getItemId() : command,
                command -> command,
                (existing, replacement) -> existing, LinkedHashMap::new
            ))
            .values()
            .stream()
            .toList();
        log.info("[handleItemCostCollectionCreatedEvent] createItemCostChangeRequestCommands size:{}",
            createItemCostChangeRequestCommands.size());

        createItemCostChangeRequestCommands.forEach(itemCostChangeRequestApplicationService::createItemCostChangeRequest);

        if (collectionParser.isUpdateAvailability()) {
            updateVendorItemAvailability(itemCostCollectionDto.getVendorId(),
                itemCostCollectionDto.getId(),
                itemCostCollectionItemParsingResultDtos);
        }

    }


    private CreateItemCostChangeRequestCommand convertToCreateItemCostChangeRequestCommand(
        ItemCostCollectionItemParsingResultDto invoiceItem,
        VendorItemMappingDto itemMapping, UUID itemCostCollectionId) {

        MatchedType matchedType = itemMapping.getMatchedType();
        boolean isMissMatched = matchedType.equals(MatchedType.MISS_MATCHED);
        boolean isVernonSales = itemMapping.getVendorName().equals(VendorConstant.VERNON_SALES);
        boolean isAutoMatchedAndUpdated = matchedType.equals(MatchedType.AUTO_MATCHED_AND_UPDATED);
        boolean isDirectCost = CostType.DIRECT_COST.getCostTypeName().equals(itemMapping.getCostType());

        Item item = null;
        if (!isMissMatched) {
            item = Optional.ofNullable(itemService.findById(itemMapping.getItemId()))
                .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND.getCode()));
            if (!COST_NOT_INCLUDE_CRV_SUPPLIERS.contains(
                itemMapping.getVendorName())) {
                invoiceItem.setCost(subtractCRV(invoiceItem.getCost(), itemMapping.getItemId()));
            }
        }

        if (isVernonSales && isAutoMatchedAndUpdated && null != item && !isDirectCost && null != invoiceItem.getCost()) {
            invoiceItem.setCost(invoiceItem.getCost().multiply(BigDecimal.valueOf(item.getPackageSize())));
        }

        String vendorItemUpc = invoiceItem.getUpc();
        if (vendorItemUpc != null && vendorItemUpc.length() > 254) {
            log.warn("vendorItemUpc is too long, will be truncated to 254 characters");
            vendorItemUpc = vendorItemUpc.substring(0, 254);
        }

        return CreateItemCostChangeRequestCommand.builder()
            .vendorSkuNumber(invoiceItem.getVendorSkuNumber())
            .vendorItemName(invoiceItem.getVendorItemName())
            .status(isMissMatched ? ItemCostChangeRequestStatus.INVALID : ItemCostChangeRequestStatus.PENDING)
            .vendorId(itemMapping.getVendorId())
            .tax(invoiceItem.getTax())
            .crv(invoiceItem.getCrv())
            .previousCost(itemMapping.getPreviousCost())
            .targetCost(invoiceItem.getCost())
            .itemCostCollectionId(itemCostCollectionId)
            .itemId(itemMapping.getItemId())
            .skuNumber(null != item ? item.getSkuNumber() : null)
            .matchType(itemMapping.getMatchedType())
            .vendorItemUpc(vendorItemUpc)
            .availability(invoiceItem.getAvailability())
            .costType(itemMapping.getCostType())
            .aisle(invoiceItem.getAisle())
            .build();
    }

    private void updateVendorItemAvailability(UUID vendorId,
        UUID itemCostChangeRequestId,
        List<ItemCostCollectionItemParsingResultDto> itemCostCollectionItemParsingResult) {
        Vendor vendor = vendorService.findById(vendorId);
        List<VendorItem> vendorItems = vendorItemService.findByVendorID(vendor.getId());

        // Process items that are not matched, set them as unavailable
        vendorItems.stream()
            .filter(vendorItem -> Boolean.TRUE.equals(vendorItem.getAvailability()))
            .filter(vendorItem -> itemCostCollectionItemParsingResult.stream()
                .noneMatch(item -> item.getVendorSkuNumber().equals(vendorItem.getVendorSkuNumber())))
            .forEach(vendorItem -> {
                log.info("Processing Vendor Item Availability for vendor item number: {} - Not matched, setting to unavailable",
                    vendorItem.getVendorSkuNumber());
                updateVendorItemAvailabilityStatus(vendorItem, itemCostChangeRequestId);
            });
    }

    private void updateVendorItemAvailabilityStatus(VendorItem vendorItem, UUID itemCostChangeRequestId) {
        UpdateVendorItemCommand updateVendorItemCommand = UpdateVendorItemCommand.builder()
            .vendorItemId(vendorItem.getId())
            .vendorSkuNumber(vendorItem.getVendorSkuNumber())
            .vendorItemName(vendorItem.getVendorItemName())
            .cost(vendorItem.getCost())
            .backupCost(vendorItem.getBackupPackPlusCrvCost())
            .note(vendorItem.getNote())
            .itemCostChangeRequestId(itemCostChangeRequestId)
            .aisle(vendorItem.getAisle())
            .isCostRefreshed(false)
            .isBackupCostRefreshed(false)
            .availability(false)
            .build();
        vendorItemApplicationService.update(updateVendorItemCommand);
    }

    private BigDecimal subtractCRV(BigDecimal newCost, UUID itemId) {
        ItemRegPrice itemRegPrice = itemRegPriceService.findByItemId(itemId);
        if (newCost != null && itemRegPrice != null) {
            return newCost.subtract(itemRegPrice.getCrvAmount());
        }
        return newCost;
    }

}
