package com.mercaso.ims.infrastructure.excel.processor;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.infrastructure.excel.data.CoremarkDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.listener.CoremarkItemDailyUpdatedDataListener;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CoremarkItemDailyUpdateSheetProcessor {

    private final DocumentApplicationService documentApplicationService;

    public List<CoremarkDailyUpdatedData> process(String fileName) {
        List<CoremarkDailyUpdatedData> coremarkDailyUpdatedDataList = new ArrayList<>();
        byte[] document = documentApplicationService.downloadDocument(fileName);
        try (ExcelReader excelReader = EasyExcelFactory.read(new ByteArrayInputStream(document)).build()) {

            ReadSheet vendorItemCostSheet = EasyExcelFactory.readSheet(0)
                .head(CoremarkDailyUpdatedData.class)
                .registerReadListener(new CoremarkItemDailyUpdatedDataListener(
                    coremarkDailyUpdatedDataList))
                .build();
            excelReader.read(vendorItemCostSheet);
        }
        return coremarkDailyUpdatedDataList;
    }
}
