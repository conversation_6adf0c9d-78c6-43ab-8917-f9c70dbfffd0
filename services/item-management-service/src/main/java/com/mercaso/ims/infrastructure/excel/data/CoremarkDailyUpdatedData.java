package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoremarkDailyUpdatedData {
    
    @ExcelProperty("name")
    private String name;
    
    @ExcelProperty("availability")
    private String availability;
    
    @ExcelProperty("SKU")
    private String sku;
    
    @ExcelProperty("upc")
    private String upc;
    
    @ExcelProperty("discontinued")
    private String discontinued;
    
    @ExcelProperty("returnable")
    private String returnable;
    
    @ExcelProperty("unitSize")
    private String unitSize;
    
    @ExcelProperty("case_cost")
    private BigDecimal caseCost;
    
    @ExcelProperty("each_cost")
    private BigDecimal eachCost;
    
    @ExcelProperty("srp")
    private BigDecimal srp;
    
    @ExcelProperty("grossProfit")
    private String grossProfit;
    
    @ExcelProperty("isBrokenCaseAllowed")
    private String isBrokenCaseAllowed;
    
    @ExcelProperty("minimum_order_quantity_MOQ")
    private String minimumOrderQuantityMoq;
    
    @ExcelProperty("image_URL")
    private String imageUrl;
}
