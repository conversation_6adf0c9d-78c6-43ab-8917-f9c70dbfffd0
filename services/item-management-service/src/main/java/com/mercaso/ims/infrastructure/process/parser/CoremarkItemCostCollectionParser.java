package com.mercaso.ims.infrastructure.process.parser;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.excel.data.CoremarkDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.processor.CoremarkItemDailyUpdateSheetProcessor;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class CoremarkItemCostCollectionParser implements ItemCostCollectionParser {

    private final ItemCostCollectionService itemCostCollectionService;
    private final CoremarkItemDailyUpdateSheetProcessor coremarkItemDailyUpdateSheetProcessor;

    @Override
    public List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(itemCostCollectionId);

        if (null == itemCostCollection) {
            return new ArrayList<>();
        }
        List<CoremarkDailyUpdatedData> coremarkDailyUpdatedDataList = coremarkItemDailyUpdateSheetProcessor.process(
            itemCostCollection.getFileName());
        return coremarkDailyUpdatedDataList.stream().map(this::convertToItemCostCollectionItemParsingResultDto).toList();
    }

    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return VendorConstant.COREMARK.equals(vendorName) && !sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public boolean isUpdateAvailability() {
        return true;
    }

    private ItemCostCollectionItemParsingResultDto convertToItemCostCollectionItemParsingResultDto(CoremarkDailyUpdatedData data) {
        // Determine availability based on the availability field and discontinued status
        // High Availability = TRUE (available)
        // Out of Stock, Low Availability = FALSE
        boolean availability = "High Availability".equalsIgnoreCase(data.getAvailability()) 
            && !"TRUE".equalsIgnoreCase(data.getDiscontinued());
        
        // Use case_cost as the primary cost, fallback to each_cost if case_cost is null
        BigDecimal cost = data.getCaseCost() != null ? data.getCaseCost() : data.getEachCost();
        
        // Convert unitSize to Integer for packSize
        Integer packSize = null;
        if (data.getUnitSize() != null && !data.getUnitSize().trim().isEmpty()) {
            try {
                packSize = Integer.parseInt(data.getUnitSize().trim());
            } catch (NumberFormatException e) {
                log.warn("Could not parse unitSize '{}' to Integer for SKU: {}", data.getUnitSize(), data.getSku());
            }
        }
        
        return ItemCostCollectionItemParsingResultDto.builder()
            .upc(data.getUpc())
            .cost(cost)
            .vendorItemName(data.getName())
            .vendorSkuNumber(data.getSku())
            .availability(availability)
            .packSize(packSize)
            .build();
    }
}
