package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemAdjustmentRequestDetailCommand extends BaseCommand {

    private UUID requestId;
    private ItemAdjustmentType type;
    private ItemAdjustmentStatus status;
    private String sku;
    private AvailabilityStatus itemStatus;
    private String aisle;
    private String primaryPoVendor;
    private String primaryJitVendor;
    private String title;
    private Integer packageSize;
    private String itemSize;
    private String itemUnitMeasure;
    private String department;
    private String category;
    private String subCategory;
    private String classType;
    private String brand;
    private BigDecimal regPricePackNoCrv;
    private BigDecimal poVendorItemCost;
    private BigDecimal jitVendorItemCost;
    private BigDecimal primaryPoVendorItemCost;
    private BigDecimal primaryJitVendorItemCost;
    private Long inventory;
    private String caseUpc;
    private String eachUpc;
    private String missingEachUPCReason;
    private String missingCaseUPCReason;
    private String vendorItemNumber;
    private String disposition;
    private String failureReason;
    private String vendor;
    private String vendorAisle;
    private String attributeName;
    private String attributeValue;
    private String companyId;
    private String locationId;

    private String newDescription;
    private Boolean promoFlag;
    private BigDecimal promoPricePackNoCrv;
    private Boolean crvFlag;
    private String imageUrl;
    private String tags;
    private Double length;
    private Double height;
    private Double width;
    private Double caseWeight;
    private Boolean cooler;
    private Boolean highValue;
    private String caseWeightUnit;

    private Boolean vendorItemAvailability;
    private String vendorItemType;

    private Double eachWeight;
    private String eachWeightUnit;

    private String archivedReason;

}
