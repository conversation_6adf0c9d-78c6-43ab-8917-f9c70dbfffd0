package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemAdjustmentRequestDetailDto extends BaseDto {

    private UUID id;
    private UUID requestId;
    private ItemAdjustmentType type;
    private ItemAdjustmentStatus status;
    private String sku;
    private AvailabilityStatus itemStatus;
    private String primaryVendorItemAisle;
    private String primaryPoVendor;
    private String primaryJitVendor;
    private String title;
    private Integer packageSize;
    private String itemSize;
    private String department;
    private String category;
    private String subCategory;
    private String classType;
    private String brand;
    private BigDecimal regPricePackNoCrv;
    private BigDecimal primaryPoVendorItemCost;
    private BigDecimal primaryJitVendorItemCost;
    private BigDecimal poVendorItemCost;
    private BigDecimal jitVendorItemCost;
    private Long inventory;
    private String upc;
    private String caseUpc;
    private String eachUpc;
    private String missingEachUPCReason;
    private String missingCaseUPCReason;
    private String vendorItemNumber;
    private String disposition;
    private String failureReason;

    private String vendor;
    private String vendorAisle;
    private String attributeName;
    private String attributeValue;
    private String companyId;
    private String locationId;
    private String newDescription;
    private String itemUnitMeasure;
    private Boolean promoFlag;
    private BigDecimal promoPricePackNoCrv;
    private Boolean crvFlag;
    private String imageUrl;
    private String tags;
    private Double length;
    private Double height;
    private Double width;
    private Double caseWeight;
    private String caseWeightUnit;
    private Double eachWeight;
    private String eachWeightUnit;
    private Boolean vendorItemAvailability;
    private String vendorItemType;
    private Boolean cooler;
    private Boolean highValue;
    private String archivedReason;

    public boolean isPrimaryVendorItemChanged() {
        return primaryPoVendor != null || primaryVendorItemAisle != null || primaryPoVendorItemCost != null;
    }

    public boolean isBackupVendorItemChanged() {
        return primaryJitVendor != null || primaryVendorItemAisle != null || primaryJitVendorItemCost != null;
    }

    public boolean isVendorChanged() {
        return StringUtils.isNotBlank(vendor);
    }

    public Float getItemSize() {
        if (itemSize == null) {
            return null;
        }
        try {
            return Float.parseFloat(itemSize);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public boolean isItemChanged() {
        return itemStatus != null
            || StringUtils.isNotBlank(title)
            || StringUtils.isNotBlank(primaryPoVendor)
            || StringUtils.isNotBlank(primaryJitVendor)
            || packageSize != null
            || primaryPoVendorItemCost != null
            || primaryJitVendorItemCost != null
            || StringUtils.isNotBlank(department)
            || StringUtils.isNotBlank(category)
            || StringUtils.isNotBlank(subCategory)
            || StringUtils.isNotBlank(classType)
            || StringUtils.isNotBlank(brand)
            || regPricePackNoCrv != null
            || StringUtils.isNotBlank(upc)
            || StringUtils.isNotBlank(caseUpc)
            || StringUtils.isNotBlank(eachUpc)
            || StringUtils.isNotBlank(disposition)
            || StringUtils.isNotBlank(attributeName)
            || StringUtils.isNotBlank(attributeValue)
            || StringUtils.isNotBlank(companyId)
            || StringUtils.isNotBlank(locationId)
            || StringUtils.isNotBlank(newDescription)
            || StringUtils.isNotBlank(itemUnitMeasure)
            || promoFlag != null
            || promoPricePackNoCrv != null
            || crvFlag != null
            || StringUtils.isNotBlank(imageUrl)
            || StringUtils.isNotBlank(tags)
            || length != null
            || height != null
            || width != null
            || caseWeight != null
            || StringUtils.isNotBlank(caseWeightUnit)
            || eachWeight != null
            || StringUtils.isNotBlank(eachWeightUnit)
            || cooler != null
            || highValue != null
            || StringUtils.isNotBlank(missingEachUPCReason)
            || StringUtils.isNotBlank(missingCaseUPCReason)
            || StringUtils.isNotBlank(archivedReason)
            ;
    }

}