package com.mercaso.ims.domain.item.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.Arrays;

public enum ArchivedReason {
    DUPLICATE("DUPLICATE"),
    DISCONTINUED("DISCONTINUED"),
    HUMAN_ERROR("HUMAN ERROR"),
    CONFIGURATION_CHANGE("CONFIGURATION CHANGE"),
    LOW_SALES_PERFORMANCE("LOW SALES PERFORMANCE"),
    OLD_ARCHIVE("OLD ARCHIVE"),
    UNKNOWN("UNKNOWN"),

    ;

    private String description;

    ArchivedReason(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static ArchivedReason fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equalsIgnoreCase(name)).findFirst().orElse(UNKNOWN);
    }

    public static ArchivedReason fromDescriptionString(String description) {
        return Arrays.stream(values()).filter(v -> v.getDescription().equalsIgnoreCase(description)).findFirst().orElse(UNKNOWN);
    }

}
