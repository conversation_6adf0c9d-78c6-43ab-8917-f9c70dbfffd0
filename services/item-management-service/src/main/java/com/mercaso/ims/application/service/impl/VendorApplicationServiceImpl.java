package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_ALREADY_EXISTS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_HAS_ASSOCIATED_ITEMS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_IS_EXTERNAL;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.dto.payload.VendorAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorCreatedPayloadDto;
import com.mercaso.ims.application.mapper.vendor.VendorDtoApplicationMapper;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.application.service.VendorApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorFactory;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;

import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import com.mercaso.ims.infrastructure.external.finale.enums.VendorStatusId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class VendorApplicationServiceImpl implements VendorApplicationService {

    private final VendorService vendorService;
    private final VendorDtoApplicationMapper vendorDtoApplicationMapper;
    private final FinaleApplicationService finaleApplicationService;
    private final BusinessEventService businessEventService;
    private final VendorItemService vendorItemService;

    @Override
    public VendorDto create(CreateVendorCommand command) {
        Vendor vendor = VendorFactory.createVendor(command);
        Vendor existVendor = vendorService.findByVendorName(vendor.getVendorName());
        if (null != existVendor) {
            throw new ImsBusinessException(VENDOR_ALREADY_EXISTS);
        }
        FinaleVendorDto finaleVendorDto = finaleApplicationService.createVendor(command.getVendorName());
        if (finaleVendorDto == null) {
            log.error("[create] finaleVendorDto is null, vendorName: {}", command.getVendorName());
        } else {
            vendor.updateFinaleId(finaleVendorDto.getPartyId());
        }
        vendor = vendorService.save(vendor);
        VendorDto vendorDto = vendorDtoApplicationMapper.domainToDto(vendor);

        businessEventService.dispatch(VendorCreatedPayloadDto.builder()
            .vendorId(vendor.getId())
            .data(vendorDto).build()
        );
        return vendorDto;
    }

    @Override
    public VendorDto update(UpdateVendorCommand command) {
        Vendor vendor = vendorService.findById(command.getVendorId());
        if (vendor == null) {
            throw new ImsBusinessException(VENDOR_NOT_FOUND.getCode());
        }
        VendorDto previous = vendorDtoApplicationMapper.domainToDto(vendor);

        Vendor byVendorName = vendorService.findByVendorName(command.getVendorName());
        if (byVendorName != null && !byVendorName.getId().equals(command.getVendorId())) {
            throw new ImsBusinessException(VENDOR_ALREADY_EXISTS);
        }
        vendor.updateVendorName(command.getVendorName());
        vendor.setShutdownWindow(command.getShutdownWindowEnabled());
        if (command.getExternalPicking() != null) {
            vendor.setexternalPicking(command.getExternalPicking());
        }
        vendor = vendorService.update(vendor);

        VendorDto vendorDto = vendorDtoApplicationMapper.domainToDto(vendor);

        businessEventService.dispatch(VendorAmendPayloadDto.builder()
            .vendorId(vendor.getId())
            .previous(previous)
            .current(vendorDto)
            .build()
        );
        return vendorDto;
    }

    @Override
    public VendorDto delete(UUID id) {
        Vendor vendor = vendorService.findById(id);
        if (vendor == null) {
            log.error("Vendor not found for delete, id: {}", id);
            throw new ImsBusinessException(VENDOR_NOT_FOUND.getCode());
        }

        // Check if vendor is external (externalPicking = true)
        if (Boolean.TRUE.equals(vendor.getExternalPicking())) {
            log.warn("Cannot delete external vendor: {}", vendor.getVendorName());
            throw new ImsBusinessException(VENDOR_IS_EXTERNAL.getCode());
        }

        // Check if vendor has associated vendor items
        List<com.mercaso.ims.domain.vendoritem.VendorItem> vendorItems = vendorItemService.findByVendorID(id);
        if (vendorItems != null && !vendorItems.isEmpty()) {
            log.warn("Cannot delete vendor with associated items: {}, item count: {}", vendor.getVendorName(), vendorItems.size());
            throw new ImsBusinessException(VENDOR_HAS_ASSOCIATED_ITEMS.getCode());
        }

        Vendor deletedVendor = vendorService.delete(id);
        if (deletedVendor == null) {
            log.error("Vendor not found for delete, id: {}", id);
            throw new ImsBusinessException(VENDOR_NOT_FOUND.getCode());
        }

        if (deletedVendor.getFinaleId() == null) {
            log.warn("Vendor {} has no finaleId, skip delete", deletedVendor.getVendorName());
            return vendorDtoApplicationMapper.domainToDto(deletedVendor);
        }

        FinaleVendorInfoDto finaleVendorInfoDto = finaleApplicationService.getVendorById(deletedVendor.getFinaleId());
        if (null != finaleVendorInfoDto)  {
            finaleVendorInfoDto.setStatusId(VendorStatusId.PARTY_DISABLED.name());
            finaleApplicationService.updateVendor(finaleVendorInfoDto);
        }

        return vendorDtoApplicationMapper.domainToDto(deletedVendor);
    }

    @Override
    public void migrateFinaleVendor() {
        List<Vendor> vendorList = vendorService.findAll();
        for (Vendor vendor : vendorList) {
            if (vendor.getFinaleId() != null) {
                continue;
            }
            FinaleVendorDto finaleVendorDto = finaleApplicationService.getVendor(vendor.getVendorName());
            if (finaleVendorDto != null) {
                vendor.updateFinaleId(finaleVendorDto.getPartyId());
                vendorService.save(vendor);
            }
        }

    }

    @Override
    public List<VendorDto> findByShutdownWindowEnabled() {
        List<Vendor> vendors = vendorService.findByShutdownWindowEnabled();
        return vendors.stream()
            .map(vendorDtoApplicationMapper::domainToDto)
            .toList();
    }

    @Override
    public VendorDto findById(UUID id) {
        Vendor vendor = vendorService.findById(id);
        if (vendor == null) {
            throw new ImsBusinessException(VENDOR_NOT_FOUND.getCode());
        }
        return vendorDtoApplicationMapper.domainToDto(vendor);
    }

    @Override
    public VendorDto updateShutdownWindow(UUID vendorId, boolean enabled, LocalTime startTime, LocalTime endTime) {
        Vendor vendor = vendorService.findById(vendorId);
        if (vendor == null) {
            throw new ImsBusinessException(VENDOR_NOT_FOUND.getCode());
        }

        vendor.setShutdownWindowEnabled(enabled);
        if (enabled) {
            vendor.setShutdownWindowStart(startTime);
            vendor.setShutdownWindowEnd(endTime);
        } else {
            vendor.setShutdownWindowStart(null);
            vendor.setShutdownWindowEnd(null);
        }

        vendor = vendorService.save(vendor);
        return vendorDtoApplicationMapper.domainToDto(vendor);
    }
}
