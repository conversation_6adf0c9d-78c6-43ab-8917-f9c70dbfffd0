package com.mercaso.ims.infrastructure.process.matcher;

import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CoremarkVendorItemMatcher extends JitVendorItemMatcher {

    public CoremarkVendorItemMatcher(VendorItemService vendorItemService, VendorService vendorService) {
        super(vendorItemService, vendorService);
    }

    @Override
    String getVendorName() {
        return VendorConstant.COREMARK;
    }
}
