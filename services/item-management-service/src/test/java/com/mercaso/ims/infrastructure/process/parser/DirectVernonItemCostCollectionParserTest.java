package com.mercaso.ims.infrastructure.process.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinalePurchaseOrderDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinalePurchaseOrderItemDto;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DirectVernonItemCostCollectionParserTest {

    @Mock
    private ItemCostCollectionService itemCostCollectionService;

    @Mock
    private FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @InjectMocks
    private DirectVernonItemCostCollectionParser directVernonItemCostCollectionParser;

    @Test
    void testParse_Success() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderItemDto orderItem = FinalePurchaseOrderItemDto.builder()
                .productId("PROD001")
                .unitPrice(new BigDecimal("10.00"))
                .normalizedPackingString("12 cs test")
                .build();

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(List.of(orderItem))
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
        assertEquals("PROD001", result.getFirst().getVendorSkuNumber());
    }

    @Test
    void testParse_NullItemCostCollection() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(null);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_InvalidSource() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.VERNON_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_InvalidType() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.CSV_FILE);

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_NullPurchaseOrder() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(null);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_WithoutConversionFactor() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderItemDto orderItem = FinalePurchaseOrderItemDto.builder()
                .productId("PROD001")
                .unitPrice(new BigDecimal("10.00"))
                .normalizedPackingString(null) // No packing string
                .build();

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(List.of(orderItem))
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
        assertEquals("PROD001", result.getFirst().getVendorSkuNumber());
        assertEquals(new BigDecimal("10.00"), result.getFirst().getCost());
    }

    @Test
    void testParse_WithInvalidPackingString() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderItemDto orderItem = FinalePurchaseOrderItemDto.builder()
                .productId("PROD001")
                .unitPrice(new BigDecimal("10.00"))
                .normalizedPackingString("invalid packing string") // Invalid format
                .build();

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(List.of(orderItem))
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
        assertEquals("PROD001", result.getFirst().getVendorSkuNumber());
        assertEquals(new BigDecimal("10.00"), result.getFirst().getCost()); // Should return original price
    }

    @Test
    void testParse_EmptyOrderItemList() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(new ArrayList<>())
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testIsSupported_True() {
        // Act
        boolean result = directVernonItemCostCollectionParser.isSupported("VERNON", ItemCostCollectionSources.FINALE_PURCHASE_ORDER);

        // Assert
        assertTrue(result);
    }

    @Test
    void testIsSupported_False() {
        // Act
        boolean result = directVernonItemCostCollectionParser.isSupported("VERNON", ItemCostCollectionSources.VERNON_ORDER);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsUpdateAvailability() {
        // Act
        boolean result = directVernonItemCostCollectionParser.isUpdateAvailability();

        // Assert
        assertFalse(result);
    }

    @Test
    void testParse_WithDecimalConversionFactor() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderItemDto orderItem = FinalePurchaseOrderItemDto.builder()
                .productId("PROD001")
                .unitPrice(new BigDecimal("24.00"))
                .normalizedPackingString("6.5 cs test product")
                .build();

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(List.of(orderItem))
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
        assertEquals("PROD001", result.getFirst().getVendorSkuNumber());
        assertEquals(new BigDecimal("3.69"), result.getFirst().getCost());
    }

    @Test
    void testParse_MultipleOrderItems() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderItemDto orderItem1 = FinalePurchaseOrderItemDto.builder()
                .productId("PROD001")
                .unitPrice(new BigDecimal("12.00"))
                .normalizedPackingString("6 cs item1")
                .build();

        FinalePurchaseOrderItemDto orderItem2 = FinalePurchaseOrderItemDto.builder()
                .productId("PROD002")
                .unitPrice(new BigDecimal("20.00"))
                .normalizedPackingString(null) // No conversion factor
                .build();

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(List.of(orderItem1, orderItem2))
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(2, result.size());
        assertEquals("PROD001", result.get(0).getVendorSkuNumber());
        assertEquals("PROD002", result.get(1).getVendorSkuNumber());
        assertEquals(new BigDecimal("20.00"), result.get(1).getCost());
    }

    @Test
    void testParse_VerifyServiceCalls() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        FinalePurchaseOrderDto purchaseOrderDto = FinalePurchaseOrderDto.builder()
                .orderId("PO123")
                .orderItemList(new ArrayList<>())
                .build();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(finaleExternalApiAdaptor.getPurchaseOrder("PO123")).thenReturn(purchaseOrderDto);

        // Act
        directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        verify(itemCostCollectionService).findById(itemCostCollectionId);
        verify(finaleExternalApiAdaptor).getPurchaseOrder("PO123");
    }
}
