package com.mercaso.wms.domain.crossdockitem;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.util.CollectionUtils;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class CrossDockTaskItem extends BaseDomain {

    private final UUID id;

    private UUID crossDockTaskId;

    private UUID batchId;

    private String skuNumber;

    private String title;

    private UUID itemId;

    private UUID taskItemId;

    private CrossDockItemSourceEnum taskType;

    private SourceEnum source;

    private UUID shippingOrderId;

    private UUID shippingOrderItemId;

    private List<String> sequence;

    private Integer pickedQty;

    private Integer crossDockedQty;

    private String breakdownName;

    private String orderNumber;

    private String taskNumber;

    private String department;

    private String category;

    public CrossDockTaskItem create(PickingTaskItem item, PickingTask pickingTask) {
        this.batchId = pickingTask.getBatchId();
        this.skuNumber = item.getSkuNumber();
        this.title = item.getTitle();
        this.itemId = item.getItemId();
        this.taskItemId = item.getId();
        this.taskType = CrossDockItemSourceEnum.PICKING_TASK;
        this.source = pickingTask.getSource();
        this.shippingOrderId = item.getShippingOrderId();
        this.shippingOrderItemId = item.getShippingOrderItemId();
        this.pickedQty = 1;
        this.breakdownName = item.getBreakdownName();
        this.orderNumber = item.getOrderNumber();
        this.taskNumber = pickingTask.getNumber();
        this.department = item.getDepartment();
        this.category = item.getCategory();

        return this;
    }

    public CrossDockTaskItem create(ReceivingTaskItem item, ReceivingTask receivingTask) {
        this.batchId = receivingTask.getBatchId();
        this.skuNumber = item.getSkuNumber();
        this.title = item.getTitle();
        this.itemId = item.getItemId();
        this.taskItemId = item.getId();
        this.taskType = CrossDockItemSourceEnum.RECEIVING_TASK;
        this.source = SourceEnum.fromName(receivingTask.getVendor());
        this.shippingOrderId = item.getShippingOrderId();
        this.shippingOrderItemId = item.getShippingOrderItemId();
        this.pickedQty = 1;
        this.breakdownName = item.getBreakdownName();
        this.orderNumber = item.getOrderNumber();
        this.taskNumber = receivingTask.getNumber();
        this.department = item.getDepartment();
        this.category = item.getCategory();

        return this;
    }

    public CrossDockTaskItem create(PickingTaskItem item, PickingTask pickingTask, String sequence) {
        this.batchId = pickingTask.getBatchId();
        this.skuNumber = item.getSkuNumber();
        this.title = item.getTitle();
        this.itemId = item.getItemId();
        this.taskItemId = item.getId();
        this.taskType = CrossDockItemSourceEnum.PICKING_TASK;
        this.source = pickingTask.getSource();
        this.shippingOrderId = item.getShippingOrderId();
        this.shippingOrderItemId = item.getShippingOrderItemId();
        this.pickedQty = 1;
        this.breakdownName = item.getBreakdownName();
        this.sequence = List.of(sequence);
        this.orderNumber = item.getOrderNumber();
        this.taskNumber = pickingTask.getNumber();
        this.department = item.getDepartment();
        this.category = item.getCategory();

        return this;
    }

    public CrossDockTaskItem create(ReceivingTaskItem item, ReceivingTask receivingTask, String sequence) {
        this.batchId = receivingTask.getBatchId();
        this.skuNumber = item.getSkuNumber();
        this.title = item.getTitle();
        this.itemId = item.getItemId();
        this.taskItemId = item.getId();
        this.taskType = CrossDockItemSourceEnum.RECEIVING_TASK;
        this.source = SourceEnum.fromName(receivingTask.getVendor());
        this.shippingOrderId = item.getShippingOrderId();
        this.shippingOrderItemId = item.getShippingOrderItemId();
        this.pickedQty = 1;
        this.breakdownName = item.getBreakdownName();
        this.sequence = List.of(sequence);
        this.orderNumber = item.getOrderNumber();
        this.taskNumber = receivingTask.getNumber();
        this.department = item.getDepartment();
        this.category = item.getCategory();

        return this;
    }

    public void assignSequence(String sequenceValue) {
        if (sequenceValue == null || sequenceValue.trim().isEmpty()) {
            throw new IllegalArgumentException("Sequence value cannot be null or empty");
        }

        if (!CollectionUtils.isEmpty(this.sequence)) {
            throw new IllegalStateException("Sequence has already been assigned to this item");
        }

        this.sequence = List.of(sequenceValue);
    }
} 