package com.mercaso.wms.delivery.domain.deliveryorder;

import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface DeliveryOrderRepository extends BaseDomainRepository<DeliveryOrder, UUID> {

    List<DeliveryOrder> findAllByDeliveryTaskIdAndStatusIn(UUID deliveryTaskId, List<DeliveryOrderStatus> statuses);

    DeliveryOrder findByOrderNumberAndShopifyOrderId(String orderNumber, String shopifyOrderId);

    List<DeliveryOrderDo> saveAll(List<DeliveryOrder> deliveryOrders);

    /**
     * Updates multiple delivery orders in a single batch operation.
     * This method is optimized for updating pre-processed domain objects.
     *
     * @param deliveryOrders list of delivery orders to update
     * @return list of updated delivery orders
     */
    List<DeliveryOrder> updateAll(List<DeliveryOrder> deliveryOrders);

    List<DeliveryOrder> findAllByOrderNumberIn(Collection<String> orderNumbers);

    List<DeliveryOrder> findAllByDeliveryTaskIdIn(Collection<UUID> taskIds);

    List<DeliveryOrder> findAllByDeliveryTaskId(UUID id);

    DeliveryOrder findByOrderNumber(String orderNumber);

    List<DeliveryOrder> findByDeliveryDate(String deliveryDate);
}