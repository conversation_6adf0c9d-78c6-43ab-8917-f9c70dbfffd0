package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.application.queryservice.PickingTaskItemQueryService;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.interfaces.util.SortByParseUtil;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/search/picking-task-items")
@RequiredArgsConstructor
public class SearchFailedPickingTaskItemResource {

    private final PickingTaskItemQueryService pickingTaskItemQueryService;

    @PreAuthorize("hasAuthority('wms:read:picking-tasks')")
    @GetMapping("/failed")
    public Result<FailedPickingTaskItemDto> search(
        @RequestParam(value = "page", defaultValue = "1", required = false) int page,
        @RequestParam(value = "pageSize", defaultValue = "50", required = false) int pageSize,
        @RequestParam(value = "deliveryDate", required = false) String deliveryDate,
        @RequestParam(value = "pickingTaskNumbers", required = false) List<String> pickingTaskNumbers,
        @RequestParam(value = "source", required = false) String source,
        @RequestParam(value = "sortTypes", required = false) List<SortType> sortTypes) {

        Sort sort = Objects.requireNonNull(SortByParseUtil.getCamelCaseSortFields(
            CollectionUtils.isEmpty(sortTypes) ? List.of(SortType.UPDATED_AT_DESC) : sortTypes,
            EntityEnums.FAILED_PICKING_TASK_ITEM));

        Page<FailedPickingTaskItemDto> failedPickingTaskItems = pickingTaskItemQueryService.searchBy(deliveryDate,
            CollectionUtils.isEmpty(pickingTaskNumbers) ? null : pickingTaskNumbers.stream().map(String::toUpperCase).toList(),
            source,
            PageRequest.of(page - 1, pageSize, sort));
        return new Result<>(failedPickingTaskItems.getContent(),
            failedPickingTaskItems.getTotalElements());
    }

}
