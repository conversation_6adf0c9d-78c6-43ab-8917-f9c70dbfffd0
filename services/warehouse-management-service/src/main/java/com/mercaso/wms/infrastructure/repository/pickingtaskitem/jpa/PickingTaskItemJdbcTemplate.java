package com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa;

import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PickingTaskItemJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<FailedPickingTaskItemDto> searchBy(String deliveryDate,
        List<String> pickingTaskNumbers,
        String source,
        Pageable pageable) {
        String availableInventoryStock = """
            with available_inventory_stock as (
                select stock.sku_number, stock.location_id, stock.qty, stock.status, l.name as location_name
                from inventory_stock stock
                left join location l on l.id = stock.location_id
                where stock.status = 'AVAILABLE' and stock.deleted_at is null and stock.qty > 0
            )
            """;
        StringBuilder sql = new StringBuilder();
        sql.append(availableInventoryStock)
            .append("select pti.id as id, pti.picking_task_id as pickingTaskId, pti.order_number as orderNumber, ")
            .append("pti.sku_number as skuNumber, pti.title as title, pti.location_name as locationName, ")
            .append("pti.aisle_number as aisleNumber, pti.expect_qty as expectQty, pti.picked_qty as pickedQty, ")
            .append("pti.error_info as errorInfo, pti.breakdown_name as breakdownName, pti.department as department, ")
            .append("pti.item_id as itemId, pt.number as pickingTaskNumber, b.tag as deliveryDate, ")
            .append("pt.source as source, pt.picker_user_name as pickerUserName, ")
            .append("STRING_AGG(DISTINCT stock.location_name,',' ORDER BY stock.location_name) AS suggestLocationNames ")
            .append("from picking_task_items pti ")
            .append("left join picking_task pt on pt.id = pti.picking_task_id ")
            .append("left join batch b on b.id = pt.batch_id ")
            .append("left join available_inventory_stock stock on stock.sku_number = pti.sku_number ")
            .append("where pti.error_info is not null ")
            .append("and pt.deleted_at is null ")
            .append("and pti.deleted_at is null ");

        StringBuilder countSql = new StringBuilder();
        countSql.append("select count(*) from picking_task_items pti ")
            .append("left join picking_task pt on pt.id = pti.picking_task_id ")
            .append("left join batch b on b.id = pt.batch_id ")
            .append("where pti.error_info is not null ")
            .append("and pt.deleted_at is null ")
            .append("and pti.deleted_at is null ");

        if (deliveryDate != null) {
            sql.append("and b.tag = :deliveryDate ");
            countSql.append("and b.tag = :deliveryDate ");
        }

        if (CollectionUtils.isNotEmpty(pickingTaskNumbers)) {
            sql.append("and pt.number IN (:pickingTaskNumbers) ");
            countSql.append("and pt.number IN (:pickingTaskNumbers) ");
        }

        if (source != null) {
            sql.append("and pt.source = :source ");
            countSql.append("and pt.source = :source ");
        }
        sql.append("group by pti.id, pt.number, pti.picking_task_id, pti.order_number, pti.sku_number, pti.title, ");
        sql.append("pti.location_name, pti.aisle_number, pti.expect_qty, pti.picked_qty, pti.error_info, pti.breakdown_name, ");
        sql.append("pti.department, pti.item_id, pti.id, b.tag, pt.source, pt.picker_user_name, pti.updated_at ");

        if (pageable.getSort().isSorted()) {
            sql.append("order by ");
            List<String> sortClauses = new ArrayList<>();
            for (Sort.Order order : pageable.getSort()) {
                String property = order.getProperty();
                String direction = order.getDirection().name();
                sortClauses.add(property + " " + direction);
            }
            sql.append(String.join(", ", sortClauses));
        } else {
            sql.append("order by pti.updated_at desc ");
        }

        sql.append(" limit :size offset :offset");

        Map<String, Object> params = new HashMap<>();
        params.put("deliveryDate", deliveryDate);
        params.put("pickingTaskNumbers", pickingTaskNumbers != null && !pickingTaskNumbers.isEmpty() ? pickingTaskNumbers : null);
        params.put("source", source);
        params.put("size", pageable.getPageSize());
        params.put("offset", pageable.getOffset());

        Long total = jdbcTemplate.queryForObject(countSql.toString(), params, Long.class);

        List<FailedPickingTaskItemDto> content = jdbcTemplate.query(
            sql.toString(),
            params,
            new BeanPropertyRowMapper<>(FailedPickingTaskItemDto.class)
        );

        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

}
