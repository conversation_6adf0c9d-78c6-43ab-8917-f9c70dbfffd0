package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.CategoryType;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdcPickingTaskCreationStrategy extends AbstractPickingTaskCreationStrategy {

    public MdcPickingTaskCreationStrategy(BatchItemQueryService batchItemQueryService) {
        super(batchItemQueryService);
    }

    @Override
    public SourceEnum getSource() {
        return SourceEnum.MDC;
    }

    @Override
    public List<BatchItem> extractData(PickingTaskCreationContext context) {
        return batchItemQueryService.findBy(context.getBatchId(), SourceEnum.MDC.name());
    }

    @Override
    public List<PickingTask> createAndSave(List<BatchItem> items, PickingTaskCreationContext context) {
        List<PickingTask> specialTasks = createSpecialLocationTasks(items, context);
        List<PickingTask> savedSpecialTasks = saveTasks(specialTasks, context.getPickingTaskRepository());
        log.info("Saved {} special location tasks for batch: {}", savedSpecialTasks.size(), context.getBatchId());

        Set<UUID> processedItemIds = specialTasks.stream()
            .flatMap(task -> task.getPickingTaskItems().stream())
            .map(PickingTaskItem::getBatchItemId)
            .collect(Collectors.toSet());

        List<BatchItem> remainingItems = items.stream()
            .filter(item -> !processedItemIds.contains(item.getId()))
            .collect(Collectors.toList());

        List<PickingTask> otherTasks = createOtherTasksFromItems(remainingItems, context);
        List<PickingTask> savedOtherTasks = saveTasks(otherTasks, context.getPickingTaskRepository());
        log.info("Saved {} other tasks for batch: {}", savedOtherTasks.size(), context.getBatchId());

        List<PickingTask> allTasks = new ArrayList<>();
        allTasks.addAll(savedSpecialTasks);
        allTasks.addAll(savedOtherTasks);
        return allTasks;
    }

    private List<PickingTask> createOtherTasksFromItems(List<BatchItem> remainingItems, PickingTaskCreationContext context) {
        List<PickingTask> tasks = new ArrayList<>();
        tasks.addAll(createOrderLevelTasks(remainingItems, context));
        tasks.addAll(createMdcBatchTasksFromItems(remainingItems, context));
        return tasks;
    }

    private List<PickingTask> createMdcBatchTasksFromItems(List<BatchItem> items, PickingTaskCreationContext context) {
        List<PickingTask> tasks = new ArrayList<>();

        List<BatchItem> nonBigOrderItems = items.stream()
            .filter(item -> !item.isBigOrder())
            .collect(Collectors.toList());

        if (nonBigOrderItems.isEmpty()) {
            return List.of();
        }

        // 1. Create small beverage tasks (MDC specific)
        List<BatchItem> smallBeverageItems = batchItemQueryService.findSmallBeverageBatchItemsByBatchId(
            context.getBatchId(), SourceEnum.MDC.name());
        if (!smallBeverageItems.isEmpty()) {
            List<BatchItem> unassignedBeverageItems = nonBigOrderItems.stream()
                .filter(item -> smallBeverageItems.stream()
                    .anyMatch(beverage -> beverage.getId().equals(item.getId())))
                .collect(Collectors.toList());

            if (!unassignedBeverageItems.isEmpty()) {
                createTasksByLocationType(unassignedBeverageItems,
                    CategoryType.MFC_BEVERAGE_CLEANING,
                    tasks,
                    context.getBatchId(),
                    context.getLocationMap());
                log.info("Found {} MDC small beverage unassigned batch items.", unassignedBeverageItems.size());
            }
        }

        // 2. Create candy tasks - group by department to avoid mixing light and heavy items
        List<BatchItem> candyItems = batchItemQueryService.findCandyBatchItemsByBatchId(
            context.getBatchId(), SourceEnum.MDC.name());
        if (!candyItems.isEmpty()) {
            List<BatchItem> unassignedCandyItems = nonBigOrderItems.stream()
                .filter(item -> candyItems.stream()
                    .anyMatch(candy -> candy.getId().equals(item.getId())))
                .collect(Collectors.toList());

            if (!unassignedCandyItems.isEmpty()) {
                unassignedCandyItems.stream()
                    .collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
                    .forEach((department, batchItems) ->
                        createTasksByLocationType(batchItems,
                            CategoryType.from(department),
                            tasks,
                            context.getBatchId(),
                            context.getLocationMap())
                    );
                log.info("Found {} MDC candy unassigned batch items.", unassignedCandyItems.size());
            }
        }
        return tasks;
    }

    @Override
    protected Comparator<BatchItem> getItemComparator() {
        return Comparator.comparing(BatchItem::getLocationName, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder()));
    }

    @Override
    protected PickingTaskType getTaskType(List<BatchItem> items) {
        long orderCount = items.stream().map(BatchItem::getOrderNumber).distinct().count();
        return orderCount == 1 ? PickingTaskType.ORDER : PickingTaskType.BATCH;
    }

    private List<PickingTask> createSpecialLocationTasks(List<BatchItem> allItems, PickingTaskCreationContext context) {
        List<PickingTask> tasks = new ArrayList<>();

        // Photo studio tasks
        List<BatchItem> photoItems = allItems.stream()
            .filter(item -> "PHOTO-STUDIO".equals(item.getLocationName()))
            .collect(Collectors.toList());
        if (!photoItems.isEmpty()) {
            photoItems.sort(Comparator.comparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));
            PickingTask task = createPickingTask(context.getBatchId(), SourceEnum.MDC,
                photoItems, context.getLocationMap());
            task.setType(PickingTaskType.BATCH);
            tasks.add(task);
            log.info("Created photo studio picking task for batch: {}", context.getBatchId());
        }

        // N/A tasks
        List<BatchItem> naItems = allItems.stream()
            .filter(item -> "N/A".equals(item.getLocationName()))
            .collect(Collectors.toList());
        if (!naItems.isEmpty()) {
            naItems.stream().collect(Collectors.groupingBy(BatchItem::getSource, Collectors.toList()))
                .forEach((source, batchItems) -> {
                    PickingTask task = createPickingTask(context.getBatchId(), SourceEnum.MDC,
                        batchItems, new HashMap<>());
                    task.setType(PickingTaskType.BATCH);
                    tasks.add(task);
                });
            log.info("Created N/A picking task for batch: {}", context.getBatchId());
        }

        // Cooler tasks
        List<BatchItem> coolerItems = allItems.stream()
            .filter(item -> BatchConstants.COOLER_LOCATION_NAME.equals(item.getLocationName()))
            .collect(Collectors.toList());
        if (!coolerItems.isEmpty()) {
            coolerItems.stream().collect(Collectors.groupingBy(BatchItem::getSource, Collectors.toList()))
                .forEach((source, batchItems) -> {
                    batchItems.sort(Comparator.comparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));
                    PickingTask task = createPickingTask(context.getBatchId(),
                        SourceEnum.fromName(source),
                        batchItems, context.getLocationMap());
                    task.setType(PickingTaskType.BATCH);
                    tasks.add(task);
                });
            log.info("Created cooler picking task for batch: {}", context.getBatchId());
        }

        return tasks;
    }

    private List<PickingTask> createOrderLevelTasks(List<BatchItem> mdcItems, PickingTaskCreationContext context) {
        List<BatchItem> bigOrderItems = mdcItems.stream()
            .filter(BatchItem::isBigOrder)
            .collect(Collectors.toList());
        if (bigOrderItems.isEmpty()) {
            return List.of();
        }

        log.info("[createOrderLevelTasks] find {} batch items", bigOrderItems.size());

        List<PickingTask> tasks = new ArrayList<>();

        processBatchItemsForOrderLevel(context.getBatchId(), tasks, context.getLocationMap(), bigOrderItems,
            CategoryType.MFC_BEVERAGE_CLEANING.getValue(), ".RD");
        processBatchItemsForOrderLevel(context.getBatchId(), tasks, context.getLocationMap(), bigOrderItems,
            null, ".TOBACCO");

        bigOrderItems.stream()
            .collect(Collectors.groupingBy(BatchItem::getOrderNumber, Collectors.toList()))
            .forEach((orderNumber, items) -> {
                PickingTask task = createPickingTask(context.getBatchId(), SourceEnum.MDC,
                    items, context.getLocationMap());
                task.setType(PickingTaskType.ORDER);
                tasks.add(task);
            });

        return tasks;
    }

    private void processBatchItemsForOrderLevel(UUID batchId, List<PickingTask> pickingTasks,
        Map<UUID, Location> locationMap, List<BatchItem> batchItems, Integer categoryLimit, String locationName) {
        List<BatchItem> filteredItems = filterLocationBatchItemsByLocationName(batchItems, locationName);
        if (!filteredItems.isEmpty()) {
            if (categoryLimit != null) {
                splitBatchItems(filteredItems, categoryLimit).forEach(items -> {
                    if (!items.isEmpty()) {
                        PickingTask task = createPickingTask(batchId, SourceEnum.MDC, items, locationMap);
                        task.setType(PickingTaskType.BATCH);
                        pickingTasks.add(task);
                    }
                });
            } else {
                PickingTask task = createPickingTask(batchId, SourceEnum.MDC, filteredItems, locationMap);
                task.setType(PickingTaskType.BATCH);
                pickingTasks.add(task);
            }
            batchItems.removeAll(filteredItems);
        }
    }

    private List<BatchItem> filterLocationBatchItemsByLocationName(List<BatchItem> batchItems, String locationName) {
        return batchItems.stream()
            .filter(batchItem -> batchItem.getLocationName().contains(locationName))
            .sorted(Comparator.comparing(BatchItem::getLocationName, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER))
                .thenComparing(BatchItem::getTitle, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER)))
            .collect(Collectors.toList());
    }

    private void createTasksByLocationType(List<BatchItem> batchItems,
        CategoryType categoryType,
        List<PickingTask> pickingTasks,
        UUID batchId,
        Map<UUID, Location> locationMap) {
        batchItems.sort(Comparator.comparing(BatchItem::getLocationName,
                Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));

        Map<String, List<BatchItem>> groupedByLocations = batchItems.stream()
            .collect(Collectors.groupingBy(batchItem -> {
                if (batchItem.getLocationName() == null) {
                    return "OTHER_GROUP";
                }
                if (batchItem.getLocationName().startsWith(".RD")) {
                    return "RD_GROUP";
                } else if (batchItem.getLocationName().startsWith(".")) {
                    return batchItem.getLocationName().replace(".", "") + "_GROUP";
                } else if (batchItem.getLocationName().contains("-")) {
                    return "AISLE_" + batchItem.getLocationName().substring(0, 3);
                } else {
                    return "OTHER_GROUP";
                }
            }));

        groupedByLocations.forEach((location, items) -> {
            splitBatchItems(items, categoryType.getValue()).forEach(splitItems -> {
                if (splitItems.isEmpty()) {
                    return;
                }
                PickingTask task = createPickingTask(batchId, getSource(), splitItems, locationMap);
                task.setType(PickingTaskType.BATCH);
                pickingTasks.add(task);
            });
        });
    }
}