package com.mercaso.wms.application.query;

import lombok.Getter;

@Getter
public enum SortType {
    CREATED_AT_DESC,
    CREATED_AT_ASC,
    ORDER_NUMBER_DESC,
    ORDER_NUMBER_ASC,
    BREAKDOWN_NAME_DESC,
    BREAKDOWN_NAME_ASC,
    AISLE_NUMBER_DESC,
    AISLE_NUMBER_ASC,
    CREATED_BY_DESC,
    CREATED_BY_ASC,
    PICKER_USER_NAME_ASC,
    PICKER_USER_NAME_DESC,
    DELIVERY_DATE_DESC,
    DELIVERY_DATE_ASC,
    ACTIVE_ORDER_DELAY_MINUTES_ASC,
    ACTIVE_ORDER_DELAY_MINUTES_DESC,
    USER_NAME_ASC,
    USER_NAME_DESC,
    UPDATED_AT_DESC,
    UPDATED_AT_ASC,
    TOTAL_LINES_ASC,
    TOTAL_LINES_DESC,
    TOTAL_QTY_ASC,
    TOTAL_QTY_DESC,
    TOTAL_PICKED_QTY_ASC,
    TOTAL_PICKED_QTY_DESC,
    DEPARTMENT_ASC,
    DEPARTMENT_DESC,
    TRUCK_NUMBER_ASC,
    TRUCK_NUMBER_DESC,
    DRIVER_USER_NAME_ASC,
    DRIVER_USER_NAME_DESC,
    SKU_NUMBER_ASC,
    SKU_NUMBER_DESC,
    TASK_NUMBER_ASC,
    TASK_NUMBER_DESC,
    SOURCE_ASC,
    SOURCE_DESC,
    LOCATION_NAME_ASC,
    LOCATION_NAME_DESC,
    UNPICKED_QTY_ASC,
    UNPICKED_QTY_DESC,
    TITLE_ASC,
    TITLE_DESC,
}
