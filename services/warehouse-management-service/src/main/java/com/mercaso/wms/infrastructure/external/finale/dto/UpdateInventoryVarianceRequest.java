package com.mercaso.wms.infrastructure.external.finale.dto;

import java.time.OffsetDateTime;
import java.util.List;
import lombok.Data;

@Data
public class UpdateInventoryVarianceRequest {
    private String statusId;
    private String physicalInventoryTypeId;
    private OffsetDateTime physicalInventoryDate;
    private String facilityUrl;
    private String generalComments;
    private List<InventoryItemVariance> inventoryItemVarianceList;
}