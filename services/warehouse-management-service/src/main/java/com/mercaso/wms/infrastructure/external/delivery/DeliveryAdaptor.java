package com.mercaso.wms.infrastructure.external.delivery;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderRouteInfoDto;
import com.mercaso.wms.delivery.application.service.DeliveryTaskService;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeliveryAdaptor {

    private final DeliveryTaskService deliveryTaskService;

    private final DeliveryOrderRepository deliveryOrderRepository;

    private final DeliveryTaskRepository deliveryTaskRepository;

    public List<DeliveryOrderRouteInfoDto> buildDeliveryTask(LocalDate deliveryDate) {
        log.info("Building delivery task for date: {}", deliveryDate);
        deliveryTaskService.buildTasks(deliveryDate);

        List<DeliveryTask> deliveryTasks = deliveryTaskRepository.findByDeliveryDate(deliveryDate.toString());
        if (deliveryTasks.isEmpty()) {
            log.warn("No delivery tasks found for date: {}", deliveryDate);
            return List.of();
        }
        Map<UUID, DeliveryTask> deliveryTaskMap = deliveryTasks.stream()
            .collect(Collectors.toMap(DeliveryTask::getId, Function.identity()));
        List<DeliveryOrderRouteInfoDto> deliveryOrderRouteInfoDtos = new ArrayList<>();
        deliveryOrderRepository.findByDeliveryDate(deliveryDate.toString())
            .forEach(deliveryOrder -> {
                if (deliveryOrder.getDeliveryTaskId() == null) {
                    log.warn("Delivery order id: {} has no associated delivery task id", deliveryOrder.getId());
                    return;
                }
                DeliveryTask deliveryTask = deliveryTaskMap.get(deliveryOrder.getDeliveryTaskId());
                if (deliveryTask == null) {
                    log.warn("No delivery task found for delivery order id: {}", deliveryOrder.getId());
                    return;
                }
                deliveryOrderRouteInfoDtos.add(
                    DeliveryOrderRouteInfoDto.builder()
                        .id(deliveryOrder.getId())
                        .driverUserId(deliveryTask.getDriverUserId())
                        .deliveryDate(deliveryOrder.getDeliveryDate())
                        .orderNumber(deliveryOrder.getOrderNumber())
                        .driverUserName(deliveryTask.getDriverUserName())
                        .truckNumber(deliveryTask.getTruckNumber())
                        .build()
                );
            });
        return deliveryOrderRouteInfoDtos;
    }

}
