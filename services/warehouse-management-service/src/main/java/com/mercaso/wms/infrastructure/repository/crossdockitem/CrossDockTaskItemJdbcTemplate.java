package com.mercaso.wms.infrastructure.repository.crossdockitem;

import com.alibaba.excel.util.StringUtils;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.application.query.CrossDockTaskItemQuery;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemStatusEnum;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CrossDockTaskItemJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchCrossDockTaskItemView> search(CrossDockTaskItemQuery criteria, Sort sort) {
        StringBuilder sql = new StringBuilder();
        StringBuilder countSql = new StringBuilder();
        MapSqlParameterSource params = new MapSqlParameterSource();

        String baseSubQuery = buildBaseSubQuery(criteria, params);

        sql.append("SELECT base_data.task_item_id, ")
            .append("base_data.batch_id, ")
            .append("base_data.sku_number, ")
            .append("base_data.title, ")
            .append("base_data.item_id, ")
            .append("base_data.task_type, ")
            .append("base_data.source, ")
            .append("base_data.shipping_order_id, ")
            .append("base_data.shipping_order_item_id, ")
            .append("base_data.sequence, ")
            .append("base_data.picked_qty, ")
            .append("base_data.cross_docked_qty, ")
            .append("base_data.breakdown_name, ")
            .append("base_data.created_at, ")
            .append("base_data.updated_at, ")
            .append("base_data.order_number, ")
            .append("base_data.task_number, ")
            .append("base_data.department, ")
            .append("base_data.category, ")
            .append("so.truck_number, ")
            .append("so.driver_user_name as driver, ")
            .append("MIN(soi.line) as line, ")
            .append("BOOL_OR(soi.high_value_item) as high_value_item ")
            .append("FROM (").append(baseSubQuery).append(") base_data ")
            .append("LEFT JOIN shipping_order_items soi on base_data.shipping_order_item_id = soi.id ")
            .append("LEFT JOIN shipping_order so on base_data.shipping_order_id = so.id ");

        countSql.append("SELECT COUNT(*) FROM (")
            .append("SELECT base_data.task_item_id ")
            .append("FROM (").append(baseSubQuery).append(") base_data ")
            .append("LEFT JOIN shipping_order_items soi on base_data.shipping_order_item_id = soi.id ")
            .append("LEFT JOIN shipping_order so on base_data.shipping_order_id = so.id ");

        List<String> whereConditions = buildWhereConditions(criteria, params);

        if (!whereConditions.isEmpty()) {
            String whereClause = "WHERE " + String.join(" AND ", whereConditions);
            sql.append(whereClause);
            countSql.append(whereClause);
        }

        sql.append(" GROUP BY base_data.task_item_id, base_data.batch_id, base_data.sku_number, ")
            .append("base_data.title, base_data.item_id, base_data.task_type, base_data.source, ")
            .append("base_data.shipping_order_id, base_data.shipping_order_item_id, base_data.sequence, ")
            .append("base_data.picked_qty, base_data.cross_docked_qty, base_data.breakdown_name, ")
            .append("base_data.created_at, base_data.updated_at, base_data.order_number, ")
            .append("base_data.task_number, base_data.department, base_data.category, ")
            .append("so.truck_number, so.driver_user_name ");

        countSql.append(") AS subquery");

        if (sort != null && sort.isSorted()) {
            sql.append("ORDER BY ");
            boolean first = true;
            for (Sort.Order order : sort) {
                if (!first) {
                    sql.append(", ");
                }
                String property = order.getProperty();
                sql.append(property).append(" ").append(order.getDirection().name());
                first = false;
            }
            sql.append(" ");
        } else {
            sql.append("ORDER BY base_data.created_at DESC ");
        }

        int page = criteria.getPage() != null ? criteria.getPage() : 1;
        int pageSize = criteria.getPageSize() != null ? criteria.getPageSize() : 20;
        int offset = (page - 1) * pageSize;

        sql.append("LIMIT :limit OFFSET :offset");
        params.addValue("limit", pageSize);
        params.addValue("offset", offset);

        Long total = jdbcTemplate.queryForObject(countSql.toString(), params, Long.class);
        List<SearchCrossDockTaskItemView> content = jdbcTemplate.query(sql.toString(), params, (rs, rowNum) -> mapToView(rs));
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private String buildBaseSubQuery(CrossDockTaskItemQuery criteria, MapSqlParameterSource params) {
        StringBuilder subQuery = new StringBuilder();

        subQuery.append("SELECT cdt_items.task_item_id as task_item_id, ")
            .append("CAST(MIN(cdt_items.batch_id::text) AS uuid) as batch_id, ")
            .append("MIN(cdt_items.sku_number) as sku_number, ")
            .append("MIN(cdt_items.title) as title, ")
            .append("CAST(MIN(cdt_items.item_id::text) AS uuid) as item_id, ")
            .append("MIN(cdt_items.task_type) as task_type, ")
            .append("MIN(cdt_items.source) as source, ")
            .append("CAST(MIN(cdt_items.shipping_order_id::text) AS uuid) as shipping_order_id, ")
            .append("CAST(MIN(cdt_items.shipping_order_item_id::text) AS uuid) as shipping_order_item_id, ")
            .append(
                "NULLIF(array_agg(s ORDER BY split_part(s, '/', 1)::integer) filter (where s is not null and cdt_items.cross_dock_task_id is not null), '{}') as sequence, ")
            .append("SUM(cdt_items.picked_qty) as picked_qty, ")
            .append("SUM(cdt_items.cross_docked_qty) as cross_docked_qty, ")
            .append("MIN(cdt_items.breakdown_name) as breakdown_name, ")
            .append("MAX(cdt_items.created_at) as created_at, ")
            .append("MAX(cdt_items.updated_at) as updated_at, ")
            .append("MIN(cdt_items.order_number) as order_number, ")
            .append("MIN(cdt_items.task_number) as task_number, ")
            .append("MIN(cdt_items.department) as department, ")
            .append("MIN(cdt_items.category) as category ")
            .append("FROM cross_dock_task_items cdt_items ")
            .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
            .append("LEFT JOIN LATERAL unnest(cdt_items.sequence) AS s ON TRUE ")
            .append("WHERE cdt_items.deleted_at IS NULL ");

        if (StringUtils.isNotBlank(criteria.getSku())) {
            subQuery.append("AND cdt_items.sku_number = :sku ");
            params.addValue("sku", criteria.getSku());
        }

        if (StringUtils.isNotBlank(criteria.getDeliveryDate())) {
            subQuery.append("AND b.tag = :deliveryDate ");
            params.addValue("deliveryDate", criteria.getDeliveryDate());
        }

        if (criteria.getPickingTaskItemId() != null) {
            subQuery.append("AND cdt_items.task_item_id = :pickingTaskItemId ");
            params.addValue("pickingTaskItemId", criteria.getPickingTaskItemId());
        }

        if (criteria.getReceivingTaskItemId() != null) {
            subQuery.append("AND cdt_items.task_item_id = :receivingTaskItemId ");
            params.addValue("receivingTaskItemId", criteria.getReceivingTaskItemId());
        }

        if (criteria.getSource() != null) {
            subQuery.append("AND cdt_items.source = :source ");
            params.addValue("source", criteria.getSource().name());
        }

        if (StringUtils.isNotBlank(criteria.getDepartment())) {
            subQuery.append("AND cdt_items.department = :department ");
            params.addValue("department", criteria.getDepartment());
        }

        if (StringUtils.isNotBlank(criteria.getCategory())) {
            subQuery.append("AND cdt_items.category = :category ");
            params.addValue("category", criteria.getCategory());
        }

        if (StringUtils.isNotBlank(criteria.getOrderNumber())) {
            subQuery.append("AND cdt_items.order_number = :orderNumber ");
            params.addValue("orderNumber", criteria.getOrderNumber());
        }

        subQuery.append("GROUP BY cdt_items.task_item_id ");

        if (criteria.getStatus() != null && CrossDockItemStatusEnum.ALL != criteria.getStatus()) {
            if (CrossDockItemStatusEnum.CROSSED == criteria.getStatus()) {
                subQuery.append("HAVING COALESCE(SUM(cdt_items.cross_docked_qty), 0) = COALESCE(SUM(cdt_items.picked_qty), 0) ");
            } else if (CrossDockItemStatusEnum.NOT_CROSSED == criteria.getStatus()
                || CrossDockItemStatusEnum.NOT_VALIDATED == criteria.getStatus()) {
                subQuery.append("HAVING COALESCE(SUM(cdt_items.cross_docked_qty), 0) != COALESCE(SUM(cdt_items.picked_qty), 0) ");
            }
        }

        return subQuery.toString();
    }

    private SearchCrossDockTaskItemView mapToView(ResultSet rs) throws SQLException {
        SearchCrossDockTaskItemView view = new SearchCrossDockTaskItemView();
        view.setBatchId(rs.getObject("batch_id", UUID.class));
        view.setSkuNumber(rs.getString("sku_number"));
        view.setTitle(rs.getString("title"));
        view.setItemId(rs.getObject("item_id", UUID.class));
        view.setTaskItemId(rs.getObject("task_item_id", UUID.class));
        view.setTaskType(rs.getString("task_type") == null ? null
            : CrossDockItemSourceEnum.valueOf(rs.getString("task_type")));
        view.setShippingOrderId(rs.getObject("shipping_order_id", UUID.class));
        view.setShippingOrderItemId(rs.getObject("shipping_order_item_id", UUID.class));
        java.sql.Array sequenceArray = rs.getArray("sequence");
        view.setSequence(sequenceArray == null ? Collections.emptyList()
            : Arrays.asList((String[]) sequenceArray.getArray()));
        view.setPickedQty(rs.getObject("picked_qty") == null ? null : rs.getInt("picked_qty"));
        view.setCrossDockedQty(rs.getObject("cross_docked_qty") == null ? null : rs.getInt("cross_docked_qty"));
        view.setBreakdownName(rs.getString("breakdown_name"));
        view.setOrderNumber(rs.getString("order_number"));
        view.setLine(rs.getObject("line") == null ? null : rs.getInt("line"));
        view.setTaskNumber(rs.getString("task_number"));
        view.setDepartment(rs.getString("department"));
        view.setCategory(rs.getString("category"));
        view.setSource(rs.getString("source"));
        view.setTruckNumber(rs.getString("truck_number"));
        view.setDriverUserName(rs.getString("driver"));
        view.setHighValueItem(rs.getObject("high_value_item") == null ? null : rs.getBoolean("high_value_item"));
        return view;
    }

    private List<String> buildWhereConditions(CrossDockTaskItemQuery criteria, MapSqlParameterSource params) {
        List<String> conditions = new ArrayList<>();

        if (StringUtils.isNotBlank(criteria.getTaskNumber())) {
            conditions.add("base_data.task_number = :taskNumber");
            params.addValue("taskNumber", criteria.getTaskNumber());
        }

        if (StringUtils.isNotBlank(criteria.getTruckNumber())) {
            conditions.add("so.truck_number = :truckNumber");
            params.addValue("truckNumber", criteria.getTruckNumber());
        }

        if (StringUtils.isNotBlank(criteria.getDriverUserName())) {
            conditions.add("so.driver_user_name = :driverUserName");
            params.addValue("driverUserName", criteria.getDriverUserName());
        }

        if (criteria.getDriverUserId() != null) {
            conditions.add("so.driver_user_id = :driverUserId");
            params.addValue("driverUserId", criteria.getDriverUserId());
        }

        if (criteria.getHighValueItem() != null) {
            conditions.add("soi.high_value_item = :highValueItem");
            params.addValue("highValueItem", criteria.getHighValueItem());
        }

        if (criteria.getStatus() != null) {
            if (CrossDockItemStatusEnum.NOT_VALIDATED == criteria.getStatus()) {
                conditions.add("so.status != :shippingOrderStatus");
                params.addValue("shippingOrderStatus", ShippingOrderStatus.VALIDATED.name());
            }
        }

        return conditions;
    }

} 