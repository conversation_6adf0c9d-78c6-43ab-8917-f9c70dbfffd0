package com.mercaso.wms.infrastructure.slackalert;

import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.NoteAttributeDto;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.customeraddress.CustomerAddressRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.infrastructure.contant.FraudOrderConstant;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class FraudOrderDetectionService {

    private final CustomerAddressRepository customerAddressRepository;
    @Value("${slack.fraud-order-alter.webhook}")
    private String slackWebhook;
    @Value("${slack.fraud-order-alter.enable}")
    private boolean enable;
    @Value("${fraud-order.ip}")
    private List<String> fraudIps;
    @Value("${fraud-order.device-id}")
    private List<String> fraudDeviceIds;
    @Value("${fraud-order.phone-number}")
    private List<String> fraudPhoneNumbers;

    public void detectFraudOrder(ShippingOrder shippingOrder, ShopifyOrderDto shopifyOrderDto) {
        if (CollectionUtils.isEmpty(shopifyOrderDto.getNoteAttributes())) {
            log.warn("[detectFraudOrder] Note attributes are null, shopify order: {}", shopifyOrderDto.getName());
            return;
        }

        List<NoteAttributeDto> noteAttributes = shopifyOrderDto.getNoteAttributes();
        String ip = getNoteAttributeValue(noteAttributes, "ip");
        String deviceId = getNoteAttributeValue(noteAttributes, "device_id");
        String phone = shopifyOrderDto.getShippingAddress() != null ? shopifyOrderDto.getShippingAddress().getPhone() : null;
        String paymentType = getNoteAttributeValue(noteAttributes, "payment_type");

        boolean isHighRisk = isHighLikelihoodFraudulentOrder(ip, deviceId, phone);
        boolean isNewUser = isNewUser(shippingOrder);
        boolean isNewAddress = isNewAddress(shippingOrder);
        boolean isACH = isACH(paymentType);

        String alertType = null;
        if (isHighRisk) {
            alertType = FraudOrderConstant.SLACK_MESSAGE_TITLE_HIGH_LIKELIHOOD_FRAUDULENT;
        } else if (isNewUser) {
            alertType = FraudOrderConstant.SLACK_MESSAGE_TITLE_NEW_USER;
        } else if (isNewAddress) {
            alertType = FraudOrderConstant.SLACK_MESSAGE_TITLE_NEW_ADDRESS;
        } else if (isACH) {
            alertType = FraudOrderConstant.SLACK_MESSAGE_TITLE_ACH;
        }
        log.info("[detectFraudOrder] alertType : {}", alertType);

        if (StringUtils.isNotEmpty(alertType)) {
            String message = buildSlackMessage(alertType,
                shopifyOrderDto,
                ip,
                deviceId,
                phone,
                paymentType,
                isIncludesTobacco(shippingOrder));
            sendFraudOrderAlert(message);
        }
    }

    private void sendFraudOrderAlert(String message) {
        if (!enable || StringUtils.isEmpty(message)) {
            return;
        }

        try {
            HttpClientUtils.executePostRequest(slackWebhook, Map.of("text", message), Map.of(), String.class);
        } catch (Exception e) {
            log.warn("[detectFraudOrder] Failed to send fraud order alert", e);
        }
    }

    private String getNoteAttributeValue(List<NoteAttributeDto> noteAttributes, String attributeName) {
        return noteAttributes.stream()
            .filter(attr -> attr.getName().equals(attributeName))
            .map(NoteAttributeDto::getValue)
            .findFirst()
            .orElse(null);
    }

    private boolean isHighLikelihoodFraudulentOrder(String ip, String deviceId, String phone) {
        return (StringUtils.isNotEmpty(ip) && !CollectionUtils.isEmpty(fraudIps) && fraudIps.contains(ip)) ||
            (StringUtils.isNotEmpty(deviceId) && !CollectionUtils.isEmpty(fraudDeviceIds) && fraudDeviceIds.contains(deviceId)) ||
            (StringUtils.isNotEmpty(phone) && !CollectionUtils.isEmpty(fraudPhoneNumbers) && fraudPhoneNumbers.contains(phone));
    }

    private boolean isACH(String paymentType) {
        return StringUtils.isNotEmpty(paymentType) && FraudOrderConstant.PAYMENT_TYPE_ACH.equals(paymentType);
    }

    private boolean isNewAddress(ShippingOrder shippingOrder) {
        CustomerAddress address = shippingOrder.getCustomerAddress();
        if (address == null) {
            return false;
        }

        if (address.getLatitude() != null && address.getLongitude() != null) {
            if (!CollectionUtils.isEmpty(customerAddressRepository.findByLatitudeAndLongitude(address.getLatitude(),
                address.getLongitude()))) {
                return false;
            }
        }

        if (StringUtils.isNoneEmpty(address.getCity(), address.getPostalCode(), address.getAddressOne())) {
            return CollectionUtils.isEmpty(customerAddressRepository.findByCityAndPostalCodeAndAddressOne(address.getCity(),
                address.getPostalCode(),
                address.getAddressOne()));
        }

        return true;
    }

    private boolean isNewUser(ShippingOrder shippingOrder) {
        CustomerAddress address = shippingOrder.getCustomerAddress();
        if (address == null || StringUtils.isAnyEmpty(address.getFirstName(), address.getLastName(), address.getPhone())) {
            return false;
        }

        return CollectionUtils.isEmpty(customerAddressRepository.findByFirstNameAndLastNameAndPhone(
            address.getFirstName(), address.getLastName(), address.getPhone()));
    }

    private String buildSlackMessage(String alertType,
        ShopifyOrderDto order,
        String ip,
        String deviceId,
        String phone,
        String paymentType,
        boolean includesTobacco) {
        String emoji = FraudOrderConstant.SLACK_MESSAGE_TITLE_HIGH_LIKELIHOOD_FRAUDULENT.equals(alertType) ? ":rotating_light:"
            : ":warning:";

        return String.format(
            """
                %s *%s* %s
                    *Order Number*: %s
                    *IP Address used*: %s
                    *Device ID*: %s
                    *Phone Number for order*: %s
                    *Payment Type*: %s
                    *Customer Note*: %s
                    *Tobacco items included*: %s
                    Click to view the order details: <%s%s>""",
            emoji, alertType, emoji,
            StringUtils.defaultString(order.getName()),
            StringUtils.defaultString(ip),
            StringUtils.defaultString(deviceId),
            StringUtils.defaultString(phone),
            StringUtils.defaultString(paymentType),
            StringUtils.defaultString(order.getNote()),
            includesTobacco ? "yes" : "no",
            FraudOrderConstant.SHOPIFY_ORDER_URL,
            StringUtils.defaultIfEmpty(order.getId(), "null")
        );
    }

    private boolean isIncludesTobacco(ShippingOrder shippingOrder) {
        return shippingOrder != null &&
            !CollectionUtils.isEmpty(shippingOrder.getShippingOrderItems()) &&
            shippingOrder.getShippingOrderItems().stream()
                .anyMatch(item -> "Tobacco".equalsIgnoreCase(item.getDepartment()));
    }
}