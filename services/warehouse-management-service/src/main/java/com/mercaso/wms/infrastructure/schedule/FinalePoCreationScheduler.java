package com.mercaso.wms.infrastructure.schedule;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.queryservice.WarehouseQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.external.finale.FinalePurchaseOrderService;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntity;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntityTypeEnum;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinalePoCreationScheduler {

    private final BatchRepository batchRepository;
    private final PgAdvisoryLock pgAdvisoryLock;
    private final FeatureFlagsManager featureFlagsManager;
    private final WarehouseQueryService warehouseQueryService;
    private final FinalePurchaseOrderService finalePurchaseOrderService;

    private static final int LOCK_KEY = "FinalePoCreationScheduler.createPurchaseOrders".hashCode();

    @Scheduled(cron = "0 30 * * * *", zone = "America/Los_Angeles")
    public void createPurchaseOrders() {
        createPurchaseOrders(null);
    }

    public void createPurchaseOrders(String deliveryDate) {
        try {
            log.info("[createPurchaseOrders] Scheduler started");
            if (StringUtils.isEmpty(deliveryDate)) {
                deliveryDate = DateUtils.getNextDeliveryDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            }
            log.info("[createPurchaseOrders] Delivery date: {}", deliveryDate);

            if (!pgAdvisoryLock.tryLockWithSessionLevel(LOCK_KEY)) {
                return;
            }

            List<Batch> batches = batchRepository.findByTag(deliveryDate);
            if (CollectionUtils.isEmpty(batches)) {
                log.info("[createPurchaseOrders] No batches found for date: {}", deliveryDate);
                return;
            }

            List<WarehouseDto> warehouses = warehouseQueryService.findByType(WarehouseType.EXTERNAL);
            batches.forEach(batch -> processBatchForWarehouses(batch, warehouses));
        } finally {
            pgAdvisoryLock.unLock(LOCK_KEY);
        }

    }

    private void processBatchForWarehouses(Batch batch, List<WarehouseDto> warehouses) {
        warehouses.forEach(warehouse -> {
            SourceEnum sourceEnum = SourceEnum.fromName(warehouse.getName());
            if (shouldCreatePurchaseOrder(batch, sourceEnum)) {
                finalePurchaseOrderService.createPurchaseOrder(sourceEnum, batch);
            } else {
                log.info("[createPurchaseOrders] PO already created for batch: {} and supplier: {}", batch.getId(), sourceEnum);
            }
        });
    }

    private boolean shouldCreatePurchaseOrder(Batch batch, SourceEnum source) {
        if (!featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_FINALE_PURCHASE_ORDER_FEATURE)) {
            log.info("[createPurchaseOrders] Feature flag is off: {}", FeatureFlagKeys.USE_FINALE_PURCHASE_ORDER_FEATURE);
            return false;
        }

        JsonNode finaleEntitiesNode = batch.getFinaleEntities();
        if (finaleEntitiesNode == null || finaleEntitiesNode.isEmpty()) {
            log.info("[shouldCreatePurchaseOrder] No finale entities found for batch: {}", batch.getId());
            return true;
        }

        List<FinaleEntity> finaleEntityList = new ObjectMapper().convertValue(
            finaleEntitiesNode, new TypeReference<>(){});

        return finaleEntityList.stream()
            .noneMatch(entity -> entity.getEntityType() == FinaleEntityTypeEnum.PURCHASE_ORDER
                && StringUtils.isNotEmpty(entity.getVendorName())
                && entity.getVendorName().equals(source.name()));
    }

}