package com.mercaso.wms.application.query;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShippingOrderQuery {

    private String orderNumber;

    private String deliveryDate;

    private String breakdownName;

    private List<String> statuses;

    private UUID driverUserId;

    private String truckNumber;

    private Boolean hasHighValueItems;

    private Boolean qaRequired;

    private SortType sort;

    @Min(value = 1, message = "Page number must be greater than 0")
    private int page;

    @Min(value = 1, message = "Page size must be greater than 0")
    @Max(value = 1000, message = "Page size must be less than or equal to 1000")
    private int pageSize;

}
