package com.mercaso.wms.application.dto.view;

import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchShippingOrderView {

    private UUID id;
    private String orderNumber;
    private String shopifyOrderId;
    private Instant orderDate;
    private String deliveryDate;
    private Instant shippedDate;
    private ShippingOrderStatus status;
    private String breakdownName;
    private Integer palletCount;
    private int originalTotalQty;
    private int currentTotalQty;
    private int pickedTotalQty;
    private int fulfilledTotalQty;
    private int validatedTotalQty;
    private String driverUserName;
    private String truckNumber;
    private boolean hasHighValueItems;
} 