package com.mercaso.wms.application.service;

import static com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus.CANCELED;
import static com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus.CAN_BE_VALIDATE_STATUSES;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderHighValueItemValidateCommand;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderValidateCommand;
import com.mercaso.wms.application.command.shippingorder.UpdateShippingOrderItemCommand;
import com.mercaso.wms.application.dto.event.ShippingOrderStartedPayloadDto;
import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderRouteInfoDto;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderService;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.delivery.DeliveryAdaptor;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import com.mercaso.wms.infrastructure.slackalert.FraudOrderDetectionService;
import com.mercaso.wms.infrastructure.slackalert.WmsExceptionAlertService;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShippingOrderApplicationService {

    private final ShippingOrderRepository shippingOrderRepository;

    private final PickingTaskRepository pickingTaskRepository;

    private final WarehouseRepository warehouseRepository;

    private final ImsAdaptor imsAdaptor;

    private final BusinessEventDispatcher businessEventDispatcher;

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final ReceivingTaskRepository receivingTaskRepository;

    private final ShippingOrderService shippingOrderService;

    private final FraudOrderDetectionService fraudOrderDetectionService;

    private final WmsExceptionAlertService wmsExceptionAlertService;

    private final DeliveryAdaptor deliveryAdaptor;

    private static final String SHOPIFY_ORDER_PREFIX = "M-";

    @RetryableTransaction
    public ShippingOrderDto validate(UUID shippingOrderId, ShippingOrderValidateCommand command) {
        ShippingOrder shippingOrder = shippingOrderRepository.findById(shippingOrderId);
        if (shippingOrder == null) {
            throw new WmsBusinessException("Shipping order not found with id: " + shippingOrderId);
        }
        validateStatus(shippingOrderId, shippingOrder);
        shippingOrder.updateDeliveredItems(command);
        ShippingOrderDto shippingOrderDto = shippingOrderDtoApplicationMapper.domainToDto(
            shippingOrderRepository.update(shippingOrder));
        shippingOrderService.saveBusinessEvent(shippingOrderDto, false, true);
        return shippingOrderDto;
    }

    @RetryableTransaction
    public List<ShippingOrderDto> validateHighValueItems(ShippingOrderHighValueItemValidateCommand command) {
        List<ShippingOrder> shippingOrders = new ArrayList<>();
        command.getUpdateShippingOrderItemCommands().stream().collect(Collectors.groupingBy(
            UpdateShippingOrderItemCommand::getShippingOrderId,
            Collectors.toList()
        )).forEach((shippingOrderId, updateCommands) -> {
            ShippingOrder shippingOrder = shippingOrderRepository.findById(shippingOrderId);
            if (shippingOrder == null) {
                throw new WmsBusinessException(ErrorCodeEnums.SHIPPING_ORDER_NOT_FOUND.getCode(),
                    ErrorCodeEnums.SHIPPING_ORDER_NOT_FOUND.getMessage());
            }
            validateStatus(shippingOrderId, shippingOrder);
            shippingOrder.updateHighValueDeliveredItems(updateCommands);
            shippingOrders.add(shippingOrder);
        });
        List<ShippingOrderDto> shippingOrderDtos = shippingOrderDtoApplicationMapper.domainToDtos(
            shippingOrderRepository.updateAll(shippingOrders));
        shippingOrderDtos.forEach(shippingOrderDto -> shippingOrderService.saveBusinessEvent(
            shippingOrderDto, false, false));
        return shippingOrderDtos;
    }

    private static void validateStatus(UUID shippingOrderId, ShippingOrder shippingOrder) {
        if (!CAN_BE_VALIDATE_STATUSES.contains(shippingOrder.getStatus())) {
            throw new WmsBusinessException("Shipping order is not in correct status: " + shippingOrderId);
        }
    }

    @RetryableTransaction
    public void createOrUpdate(ShopifyOrderDto shopifyOrderDto) {
        boolean mfcOrder = ShippingOrder.builder().build().isMfcOrder(shopifyOrderDto.getTags());
        if (!mfcOrder) {
            log.info("Ignoring non-MFC order: {}", shopifyOrderDto);
            return;
        }
        LocalDate deliveryDate = ShippingOrder.builder().build().convertDeliveryDate(shopifyOrderDto.getTags());
        if (deliveryDate != null && deliveryDate.isBefore(LocalDate.now().minusMonths(1))) {
            return;
        }
        if (shopifyOrderDto.getName() != null && shopifyOrderDto.getName().contains("M-")) {
            shopifyOrderDto.setName(shopifyOrderDto.getName().replaceFirst("M-", ""));
        }
        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock((shopifyOrderDto.getName() + shopifyOrderDto.getId()).hashCode(),
            "ShippingOrderApplicationService.createOrUpdate");
        ShippingOrder shippingOrder = shippingOrderRepository.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());
        boolean firstCreate;
        if (null == shippingOrder) {
            shippingOrder = ShippingOrder.builder().build().create(shopifyOrderDto);
            setWarehouse(shippingOrder);
            firstCreate = true;
        } else {
            shippingOrder.update(shopifyOrderDto);
            firstCreate = false;
        }
        setFieldsFromIms(shippingOrder);
        if (firstCreate) {
            // Check for fraudulent orders only if the order is not created
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);
        }

        shippingOrderService.setCustomerAddress(shippingOrder);
        ShippingOrderDto shippingOrderDto = shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.save(
            shippingOrder));
        shippingOrderService.saveBusinessEvent(shippingOrderDto, firstCreate, false);

        // Check for order created after batch creation
        wmsExceptionAlertService.checkOrderAfterBatchCreation(shippingOrder);
    }

    public void update(OutboundScanRecordDto dto) {
        ShippingOrder shippingOrder = shippingOrderRepository.findById(dto.getShippingOrderId());
        if (shippingOrder == null) {
            log.error("Shipping order not found with id: {}", dto.getShippingOrderId());
            return;
        }
        shippingOrder.picked(dto);
        shippingOrderService.saveBusinessEvent(shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.update(
            shippingOrder)), false, false);
    }

    @RetryableTransaction
    public void updateOrderStatusByReceivingTaskId(UUID receivingTaskId) {
        ReceivingTask receivingTask = receivingTaskRepository.findById(receivingTaskId);
        if (receivingTask == null || receivingTask.getReceivingTaskItems().isEmpty()) {
            log.error("[updateOrderStatusByReceivingTaskId] Receiving task not found with id: {}", receivingTaskId);
            return;
        }
        List<ShippingOrder> orders = shippingOrderRepository.findByOrderIds(
            receivingTask.getReceivingTaskItems().stream()
                .map(ReceivingTaskItem::getShippingOrderId)
                .distinct()
                .toList());
        if (CollectionUtils.isEmpty(orders)) {
            log.warn("[updateOrderStatusByReceivingTaskId] Shipping order not found for receiving task: {}", receivingTaskId);
            return;
        }
        orders.forEach(order -> {
            List<ReceivingTaskItem> receivedReceivingTaskItems = receivingTask.getReceivingTaskItems().stream()
                .filter(receivedItem -> receivedItem.getShippingOrderId().equals(order.getId()))
                .toList();
            order.received(receivedReceivingTaskItems);
            log.info("[updateOrderStatusByReceivingTaskId] Order status updated for order: {}, status: {}",
                order.getOrderNumber(),
                order.getStatus());
            shippingOrderService.saveBusinessEvent(shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.update(
                order)), false, false);
        });
    }

    public void updateOrderStatusByPickingTaskId(UUID pickingTaskId) {
        PickingTask pickingTask = pickingTaskRepository.findById(pickingTaskId);
        if (pickingTask == null || pickingTask.getPickingTaskItems().isEmpty()) {
            log.error("Picking task not found with id: {}", pickingTaskId);
            return;
        }
        if (pickingTask.getType() == PickingTaskType.ORDER) {
            updateOrderStatusByOrderLevelPickingTask(pickingTask);
        } else {
            updateOrderStatusByBatchLevelPickingTask(pickingTask);
        }
    }

    private void updateOrderStatusByOrderLevelPickingTask(PickingTask pickingTask) {
        log.info("[updateOrderStatusByOrderLevelPickingTask] order level picking task: {}", pickingTask.getId());
        List<ShippingOrder> shippingOrders = shippingOrderRepository.findByOrderIds(
            pickingTask.getPickingTaskItems().stream()
                .map(PickingTaskItem::getShippingOrderId)
                .distinct()
                .toList());
        if (CollectionUtils.isEmpty(shippingOrders)) {
            log.warn("[updateOrderStatusByOrderLevelPickingTask] Shipping order not found for order picking task: {}",
                pickingTask.getId());
            return;
        }
        shippingOrderService.updateSingleOrderWithRetry(shippingOrders.getFirst().getId(),
            pickingTask.getPickingTaskItems(),
            pickingTask.getId(), pickingTask.getType());
    }

    private void updateOrderStatusByBatchLevelPickingTask(PickingTask pickingTask) {
        log.info("[updateOrderStatusByBatchLevelPickingTask] batch level picking task: {}", pickingTask.getId());
        List<UUID> orderIds = pickingTask.getPickingTaskItems().stream()
            .map(PickingTaskItem::getShippingOrderId)
            .distinct()
            .toList();

        if (orderIds.isEmpty()) {
            log.warn("[updateOrderStatusByBatchLevelPickingTask] No orders found for batch picking task: {}",
                pickingTask.getId());
            return;
        }

        List<ShippingOrder> orders = shippingOrderRepository.findByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orders)) {
            log.warn("[updateOrderStatusByBatchLevelPickingTask] Shipping orders not found for batch picking task: {}",
                pickingTask.getId());
            return;
        }
        for (ShippingOrder order : orders) {
            List<PickingTaskItem> pickedPickingTaskItems = pickingTask.getPickingTaskItems().stream()
                .filter(item -> item.getShippingOrderId().equals(order.getId()))
                .toList();

            if (pickedPickingTaskItems.isEmpty()) {
                continue;
            }

            try {
                shippingOrderService.updateSingleOrderWithRetry(order.getId(),
                    pickedPickingTaskItems,
                    pickingTask.getId(),
                    pickingTask.getType());
            } catch (Exception e) {
                log.warn("[updateOrderStatusByBatchLevelPickingTask] Failed to update order status for order: {} taskId: {}",
                    order.getOrderNumber(),
                    pickingTask.getId(),
                    e);
            }
        }
    }

    private void setWarehouse(ShippingOrder shippingOrder) {
        shippingOrder.setWarehouse(warehouseRepository.findByName("MFC"));
    }

    private void setFieldsFromIms(ShippingOrder shippingOrder) {
        Set<String> skus = shippingOrder.getShippingOrderItems()
            .stream()
            .filter(shippingOrderItem -> shippingOrderItem.getItemId() == null)
            .map(ShippingOrderItem::getSkuNumber)
            .collect(Collectors.toSet());
        if (skus.isEmpty()) {
            return;
        }
        List<ItemCategoryDto> itemsBySkus = imsAdaptor.getItemsBySkus(skus.stream().toList());
        shippingOrder.getShippingOrderItems()
            .forEach(shippingOrderItem -> itemsBySkus.stream()
                .filter(itemCategory -> Objects.equals(itemCategory.getSkuNumber(), shippingOrderItem.getSkuNumber()))
                .findFirst()
                .ifPresent(itemCategoryDto -> {
                    shippingOrderItem.setItemId(itemCategoryDto.getId());
                    shippingOrderItem.setTitle(itemCategoryDto.getTitle());
                    shippingOrderItem.setDepartment(itemCategoryDto.getDepartment());
                    shippingOrderItem.setCategory(itemCategoryDto.getCategory());
                    shippingOrderItem.setSubCategory(itemCategoryDto.getSubCategory());
                    shippingOrderItem.setPrimaryVendorId(itemCategoryDto.getPrimaryVendorId());
                    shippingOrderItem.setBackupVendorId(itemCategoryDto.getBackupVendorId());
                    shippingOrderItem.setPrimaryVendorName(itemCategoryDto.getPrimaryVendorName());
                    shippingOrderItem.setBackupVendorName(itemCategoryDto.getBackupVendorName());
                    shippingOrderItem.setVersionNumber(itemCategoryDto.getVersionNumber());
                    shippingOrderItem.setHighValueItem(isHighValueItem(itemCategoryDto));
                }));
    }

    private static boolean isHighValueItem(ItemCategoryDto itemCategoryDto) {
        if (itemCategoryDto == null || CollectionUtils.isEmpty(itemCategoryDto.getItemAttributes())) {
            return false;
        }
        return itemCategoryDto.getItemAttributes()
            .stream()
            .anyMatch(attr -> "High Value".equals(attr.getAttributeName()) && Boolean.parseBoolean(attr.getValue()));
    }

    public List<ShippingOrder> findActiveShippingOrdersByDeliveryDate(String deliveryDate) {
        return shippingOrderRepository.findActiveShippingOrdersByDeliveryDate(deliveryDate);
    }

    public void markInProgress(UUID batchId) {
        List<ShippingOrder> orders = shippingOrderRepository.findByBatchId(batchId);
        orders = orders.stream().filter(order -> order.getStatus() == ShippingOrderStatus.OPEN).toList();
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        orders.forEach(order -> {
            order.addToBatch();
            businessEventDispatcher.dispatch(BusinessEventFactory.build(ShippingOrderStartedPayloadDto.builder()
                .shippingOrderId(order.getId())
                .data(shippingOrderDtoApplicationMapper.domainToDto(order))
                .build()));
        });
        shippingOrderRepository.saveAll(orders);
    }





    private Map<String, DeliveryOrderRouteInfoDto> indexRouteInfoByOrderNumber(
        List<DeliveryOrderRouteInfoDto> routeInfos) {
        return routeInfos.stream().collect(Collectors.toMap(
            DeliveryOrderRouteInfoDto::getOrderNumber,
            dto -> dto,
            (existing, replacement) -> replacement,
            LinkedHashMap::new
        ));
    }

    private List<ShippingOrder> collectOrdersNeedingRouteUpdate(
        LocalDate deliveryDate,
        Map<String, DeliveryOrderRouteInfoDto> routeInfoByOrderNumber) {
        List<ShippingOrder> ordersToUpdate = new ArrayList<>();
        List<ShippingOrder> shippingOrders = shippingOrderRepository.findByDeliveryDate(deliveryDate.toString());
        log.info("Found {} shipping orders for delivery date: {}", shippingOrders.size(), deliveryDate);
        for (ShippingOrder shippingOrder : shippingOrders) {
            if (shippingOrder.getStatus() == CANCELED) {
                log.info("Skipping canceled shipping order: {}", shippingOrder.getOrderNumber());
                continue;
            }
            DeliveryOrderRouteInfoDto routeInfoDto =
                routeInfoByOrderNumber.get(SHOPIFY_ORDER_PREFIX + shippingOrder.getOrderNumber());
            if (routeInfoDto == null) {
                log.warn("No delivery order route info found for shipping order: {}", shippingOrder.getOrderNumber());
                continue;
            }
            log.info("Processing shipping order: {}, route info: {}", shippingOrder.getOrderNumber(), routeInfoDto);
            boolean needsUpdate = !Objects.equals(shippingOrder.getTruckNumber(), routeInfoDto.getTruckNumber());
            if (!Objects.equals(shippingOrder.getDriverUserName(), routeInfoDto.getDriverUserName())) {
                needsUpdate = true;
            }
            if (!Objects.equals(shippingOrder.getDriverUserId(), routeInfoDto.getDriverUserId())) {
                needsUpdate = true;
            }

            if (needsUpdate) {
                shippingOrder.bindRouteInfo(routeInfoDto);
                ordersToUpdate.add(shippingOrder);
                log.info("Shipping order {} needs route info update", shippingOrder.getOrderNumber());
            }
        }
        return ordersToUpdate;
    }

    @Transactional
    public void syncRouteInfo(@Valid LocalDate deliveryDate) {
        if (deliveryDate == null) {
            throw new WmsBusinessException("deliveryDate must not be null");
        }
        List<DeliveryOrderRouteInfoDto> deliveryOrderRouteInfoDtos = deliveryAdaptor.buildDeliveryTask(deliveryDate);
        if (CollectionUtils.isEmpty(deliveryOrderRouteInfoDtos)) {
            throw new WmsBusinessException("No delivery orders found for the given delivery date: " + deliveryDate);
        }

        Map<String, DeliveryOrderRouteInfoDto> routeInfoByOrderNumber =
            indexRouteInfoByOrderNumber(deliveryOrderRouteInfoDtos);

        List<ShippingOrder> needUpdateShippingOrders =
            collectOrdersNeedingRouteUpdate(deliveryDate, routeInfoByOrderNumber);

        if (CollectionUtils.isEmpty(needUpdateShippingOrders)) {
            log.info("No shipping orders found to update route info for delivery date: {}", deliveryDate);
            return;
        }
        List<ShippingOrder> updatedShippingOrders = shippingOrderRepository.updateAll(needUpdateShippingOrders);
        log.info("Updated {} shipping orders with route info for delivery date: {}", updatedShippingOrders.size(), deliveryDate);
    }
}
