package com.mercaso.wms.domain.businessevent;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EntityEnums {

    BATCH("Batch", "batchId"),
    PICKING_TASK("PickingTask", "pickingTaskId"),
    INVENTORY_STOCK("InventoryStock", "inventoryStockId"),
    SHIPPING_ORDER("ShippingOrder", "shippingOrderId"),
    SHIPPING_ORDER_ITEM("ShippingOrderItem", "shippingOrderItemId"),
    OUTBOUND_SCAN_RECORD("OutboundScanRecord", "outboundScanRecordId"),
    ACCOUNT_PREFERENCE("AccountPreference", "accountPreferenceId"),
    RECEIVING_TASK("ReceivingTask", "receivingTaskId"),
    TRANSFER_TASK("TransferTask", "transferTaskId"),
    TRANSFER_TASK_ITEM("TransferTaskItem", "transferTaskItemId"),
    PICKING_TASK_ITEM("PickingTaskItem", "pickingTaskItemId"),
    RECEIVING_TASK_ITEM("ReceivingTaskItem", "receivingTaskItemId"),
    DELIVERY_ORDER("DeliveryOrder", "deliveryOrderId"),
    DELIVERY_TASK("DeliveryTask", "deliveryTaskId"),
    ACCOUNT("Account", "accountId"),
    DELIVERY_ORDER_ITEM("DeliveryOrderItem", "deliveryOrderItemId"),
    ACTIVE_ORDER_DELAY_MINUTES("", ""),
    CROSS_DOCK_TASK_ITEM("CrossDockTaskItem", "crossDockTaskItemId"),
    FAILED_PICKING_TASK_ITEM("FailedPickingTaskItem", "failedPickingTaskItemId"),
    ;

    private final String value;
    private final String idField;

}
