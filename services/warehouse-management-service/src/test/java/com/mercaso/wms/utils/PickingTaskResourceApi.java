package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BatchAssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkCompletePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkSplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.CancelPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.ReassignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.SplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class PickingTaskResourceApi extends IntegrationTestRestUtil {

    private static final String GET_PICKING_TASK_BY_ID_URL = "/query/picking-tasks/%s";

    private static final String SEARCH_PICKING_TASKS_URL = "/search/picking-tasks";

    private static final String ASSIGN_PICKING_TASK_URL = "/picking-tasks/%s/assign";

    private static final String BATCH_ASSIGN_PICKING_TASK_URL = "/picking-tasks/batch/assign";

    private static final String REASSIGN_PICKING_TASK_URL = "/picking-tasks/%s/reassign";

    private static final String CANCEL_PICKING_TASK_URL = "/picking-tasks/%s/cancel";

    private static final String START_PICKING_TASK_URL = "/picking-tasks/%s/picking";

    private static final String COMPLETE_PICKING_TASK_URL = "/picking-tasks/%s/complete";

    private static final String UPDATE_PICKING_TASK_URL = "/picking-tasks/%s";

    private static final String SPLIT_PICKING_TASK_URL = "/picking-tasks/%s/split-task";

    private static final String BATCH_CANCEL_PICKING_TASK = "/picking-tasks/batch/cancel";

    private static final String SEARCH_PICKING_TASK_ITEMS_URL = "/search/picking-task-items/failed";

    public static final String BULK_SPLIT_PICKING_TASK_URL = "/picking-tasks/bulk-split";

    public static final String BULK_COMPLETE_PICKING_TASK_URL = "/picking-tasks/bulk-complete";

    public static final String BATCH_UNASSIGN_PICKING_TASKS_URL = "/picking-tasks/batch/unassign";

    public PickingTaskResourceApi(Environment environment) {
        super(environment);
    }

    public PickingTaskDto getPickingTask(UUID id) {
        return getEntity(String.format(GET_PICKING_TASK_BY_ID_URL, id), PickingTaskDto.class).getBody();
    }

    public Result<PickingTaskDto> searchPickingTasks(
        String batchNumber,
        List<String> numbers,
        UUID userId,
        SourceEnum source,
        PickingTaskStatus[] statuses,
        LocalDate deliveryDate,
        PickingTaskType type,
        List<String> departments,
        List<String> categories,
        List<String> skuNumbers,
        List<SortType> sortTypes) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_PICKING_TASKS_URL + "?page=1&pageSize=20");
        if (batchNumber != null) {
            url.append("&batchNumber=").append(batchNumber);
        }
        if (numbers != null) {
            url.append("&numbers=").append(String.join(",", numbers));
        }
        if (userId != null) {
            url.append("&userId=").append(userId);
        }
        if (source != null) {
            url.append("&source=").append(source);
        }
        if (statuses != null) {
            url.append("&statuses=").append(String.join(",", Arrays.stream(statuses).map(Enum::name).toArray(String[]::new)));
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (type != null) {
            url.append("&type=").append(type);
        }
        if (departments != null) {
            url.append("&departments=").append(String.join(",", departments));
        }
        if (categories != null) {
            url.append("&categories=").append(String.join(",", categories));
        }
        if (skuNumbers != null) {
            url.append("&skuNumbers=").append(String.join(",", skuNumbers));
        }
        if (!CollectionUtils.isEmpty(sortTypes)) {
            List<String> sortTypesStr = sortTypes.stream().map(Enum::toString).toList();
            url.append("&sortTypes=").append(String.join(",", sortTypesStr));
        }

        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public PickingTaskDto assignPickingTask(UUID id, AssignPickingTaskCommand command) throws JsonProcessingException {
        return updateEntity(String.format(ASSIGN_PICKING_TASK_URL, id), command, PickingTaskDto.class);
    }

    public List<PickingTaskDto> batchAssignPickingTasks(BatchAssignPickingTaskCommand command) throws JsonProcessingException {
        return updateEntities(BATCH_ASSIGN_PICKING_TASK_URL, command, PickingTaskDto.class);
    }

    public PickingTaskDto reassignPickingTask(UUID id, ReassignPickingTaskCommand command) throws JsonProcessingException {
        return updateEntity(String.format(REASSIGN_PICKING_TASK_URL, id), command, PickingTaskDto.class);
    }

    public PickingTaskDto completePickingTask(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(COMPLETE_PICKING_TASK_URL, id), null, PickingTaskDto.class);
    }

    public PickingTaskDto cancelPickingTask(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(CANCEL_PICKING_TASK_URL, id), null, PickingTaskDto.class);
    }

    public void startPickingTask(UUID id) throws JsonProcessingException {
        updateEntity(String.format(START_PICKING_TASK_URL, id), null, PickingTaskDto.class);
    }

    public PickingTaskDto updatePickingTask(UUID id, UpdatePickingTaskCommand command) throws JsonProcessingException {
        return updateEntity(String.format(UPDATE_PICKING_TASK_URL, id), command, PickingTaskDto.class);
    }

    public PickingTaskDto splitPickingTask(UUID id, SplitPickingTaskCommand command) throws JsonProcessingException {
        return updateEntity(String.format(SPLIT_PICKING_TASK_URL, id), command, PickingTaskDto.class);
    }

    public void batchCancelPickingTask(CancelPickingTaskCommand command) throws JsonProcessingException {
        updateEntity(BATCH_CANCEL_PICKING_TASK, command, Void.class);
    }

    public Result<FailedPickingTaskItemDto> searchFailedPickingTaskItems(String deliveryDate,
        List<String> pickingTaskNumbers,
        String source)
        throws Exception {
        return searchFailedPickingTaskItems(deliveryDate, pickingTaskNumbers, source, null);
    }

    public Result<FailedPickingTaskItemDto> searchFailedPickingTaskItems(String deliveryDate,
        List<String> pickingTaskNumbers,
        String source,
        List<SortType> sortTypes)
        throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_PICKING_TASK_ITEMS_URL + "?page=1&pageSize=20");
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (pickingTaskNumbers != null) {
            url.append("&pickingTaskNumbers=").append(String.join(",", pickingTaskNumbers));
        }
        if (source != null) {
            url.append("&source=").append(source);
        }
        if (!CollectionUtils.isEmpty(sortTypes)) {
            List<String> sortTypesStr = sortTypes.stream().map(Enum::toString).toList();
            url.append("&sortTypes=").append(String.join(",", sortTypesStr));
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public PickingTaskDto bulkSplitPickingTask(BulkSplitPickingTaskCommand command) throws JsonProcessingException {
        return updateEntity(BULK_SPLIT_PICKING_TASK_URL, command, PickingTaskDto.class);
    }

    public void bulkCompletePickingTask(BulkCompletePickingTaskCommand command) throws JsonProcessingException {
        updateEntity(BULK_COMPLETE_PICKING_TASK_URL, command, Void.class);
    }

    public List<PickingTaskDto> batchUnassignPickingTasks(List<UUID> pickingTaskIds) throws JsonProcessingException {
        return updateEntities(BATCH_UNASSIGN_PICKING_TASKS_URL, pickingTaskIds, PickingTaskDto.class);
    }

    public Result<PickingTaskDto> searchPickingTasks(
        String batchNumber,
        List<String> numbers,
        UUID userId,
        SourceEnum source,
        PickingTaskStatus[] statuses,
        LocalDate deliveryDate,
        PickingTaskType type,
        List<String> departments,
        List<String> categories,
        List<String> skuNumbers,
        List<SortType> sortTypes,
        Boolean isSingleItemTask) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_PICKING_TASKS_URL + "?page=1&pageSize=20");
        if (batchNumber != null) {
            url.append("&batchNumber=").append(batchNumber);
        }
        if (numbers != null) {
            url.append("&numbers=").append(String.join(",", numbers));
        }
        if (userId != null) {
            url.append("&userId=").append(userId);
        }
        if (source != null) {
            url.append("&source=").append(source);
        }
        if (statuses != null) {
            url.append("&statuses=").append(String.join(",", Arrays.stream(statuses).map(Enum::name).toArray(String[]::new)));
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (type != null) {
            url.append("&type=").append(type);
        }
        if (departments != null) {
            url.append("&departments=").append(String.join(",", departments));
        }
        if (categories != null) {
            url.append("&categories=").append(String.join(",", categories));
        }
        if (skuNumbers != null) {
            url.append("&skuNumbers=").append(String.join(",", skuNumbers));
        }
        if (!CollectionUtils.isEmpty(sortTypes)) {
            List<String> sortTypesStr = sortTypes.stream().map(Enum::toString).toList();
            url.append("&sortTypes=").append(String.join(",", sortTypesStr));
        }
        if (isSingleItemTask != null) {
            url.append("&isSingleItemTask=").append(isSingleItemTask);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

}
