package com.mercaso.wms.application.service;

import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrder;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrdersWithId;
import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderHighValueItemValidateCommand;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderValidateCommand;
import com.mercaso.wms.application.command.shippingorder.UpdateShippingOrderItemCommand;
import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderRouteInfoDto;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderService;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.delivery.DeliveryAdaptor;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.slackalert.FraudOrderDetectionService;
import com.mercaso.wms.infrastructure.slackalert.WmsExceptionAlertService;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ShippingOrderApplicationServiceTest {

    private final ShippingOrderRepository shippingOrderRepository = mock(ShippingOrderRepository.class);

    private final WarehouseRepository warehouseRepository = mock(WarehouseRepository.class);

    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final ImsAdaptor imsAdaptor = mock(ImsAdaptor.class);

    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper = mock(ShippingOrderDtoApplicationMapper.class);

    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);

    private final ReceivingTaskRepository receivingTaskRepository = mock(ReceivingTaskRepository.class);

    private final ShippingOrderService shippingOrderService = mock(ShippingOrderService.class);

    private final FraudOrderDetectionService fraudOrderDetectionService = mock(FraudOrderDetectionService.class);

    private final WmsExceptionAlertService wmsExceptionAlertService = mock(WmsExceptionAlertService.class);

    private final DeliveryAdaptor deliveryAdaptor = mock(DeliveryAdaptor.class);

    private final ShippingOrderApplicationService shippingOrderApplicationService = new ShippingOrderApplicationService(
        shippingOrderRepository,
        pickingTaskRepository,
        warehouseRepository,
        imsAdaptor,
        businessEventDispatcher,
        shippingOrderDtoApplicationMapper,
        pgAdvisoryLock, receivingTaskRepository,
        shippingOrderService,
        fraudOrderDetectionService,
        wmsExceptionAlertService,
        deliveryAdaptor);

    private UUID shippingOrderId;

    @BeforeEach
    void setUp() {
        shippingOrderId = UUID.randomUUID();
        ShippingOrder shippingOrder = ShippingOrder.builder().id(shippingOrderId).build();
        shippingOrder.setStatus(ShippingOrderStatus.PICKED);
        shippingOrder.setShippingOrderItems(
            Collections.singletonList(
                ShippingOrderItem.builder().id(UUID.randomUUID()).validatedQty(5).build()
            )
        );
    }

    @Test
    void when_createOrUpdate_given_shopifyOrderDto_then_return_shippingOrderDto() {
        // given
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        ShippingOrder shippingOrder = buildShippingOrder(shopifyOrderDto);
        // when
        when(warehouseRepository.findByType(any())).thenReturn(List.of(buildWarehouse(UUID.randomUUID())));
        when(shippingOrderRepository.findByOrderNumberAndShopifyOrderId(any(), anyString())).thenReturn(null);
        when(shippingOrderRepository.save(any())).thenReturn(shippingOrder);
        ShippingOrderDto shippingOrderDto = new ShippingOrderDto();
        shippingOrderDto.setStatus(ShippingOrderStatus.OPEN);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(shippingOrderDto);

        shippingOrderApplicationService.createOrUpdate(shopifyOrderDto);

        verify(shippingOrderService, times(1)).saveBusinessEvent(any(), anyBoolean(), anyBoolean());
    }

    @Test
    void when_picking_task_not_found_then_return() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderRepository, never()).save(any());
        verify(shippingOrderRepository, never()).saveAll(any());
    }

    @Test
    void when_order_level_picking_task_then_update_single_order() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();
        UUID orderItemId = UUID.randomUUID();

        PickingTask pickingTask = mock(PickingTask.class);
        PickingTaskItem pickingTaskItem = mock(PickingTaskItem.class);
        ShippingOrder shippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder().status(ShippingOrderStatus.PICKED).build();

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTask.getType()).thenReturn(PickingTaskType.ORDER);
        when(pickingTask.getPickingTaskItems()).thenReturn(List.of(pickingTaskItem));
        when(pickingTaskItem.getShippingOrderItemId()).thenReturn(orderItemId);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(List.of(shippingOrder));
        when(shippingOrderRepository.findById(any())).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(any())).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderService, times(1)).updateSingleOrderWithRetry(any(), anyList(), any(), any());
    }

    @Test
    void when_batch_level_picking_task_then_update_multiple_orders() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();

        PickingTask pickingTask = buildPickingTask(UUID.randomUUID(), PickingTaskType.BATCH);
        ShippingOrder shippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
        pickingTask.getPickingTaskItems().forEach(item -> item.setShippingOrderId(shippingOrder.getId()));
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder().status(ShippingOrderStatus.PICKED).build();

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(List.of(shippingOrder));
        when(shippingOrderRepository.findById(any())).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);
        when(shippingOrderRepository.update(any())).thenReturn(shippingOrder);

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderService, times(1)).updateSingleOrderWithRetry(any(), anyList(), any(), any());
    }

    @Test
    void when_order_level_picking_task_order_not_found_then_return() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();

        PickingTask pickingTask = mock(PickingTask.class);
        PickingTaskItem pickingTaskItem = mock(PickingTaskItem.class);

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTask.getType()).thenReturn(PickingTaskType.ORDER);
        when(pickingTask.getPickingTaskItems()).thenReturn(List.of(pickingTaskItem));
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(List.of());

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderRepository, never()).save(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void update_WhenShippingOrderNotFound_ShouldLogErrorAndReturn() {
        // Given
        OutboundScanRecordDto dto = new OutboundScanRecordDto();
        dto.setShippingOrderId(UUID.randomUUID());
        when(shippingOrderRepository.findById(any())).thenReturn(null);

        // When
        shippingOrderApplicationService.update(dto);

        // Then
        verify(shippingOrderRepository, times(0)).save(any());
    }

    @Test
    void update_WhenShippingOrderFound_ShouldUpdateAndSaveEvent() {
        // Given
        OutboundScanRecordDto dto = new OutboundScanRecordDto();
        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        ShippingOrderDto shippingOrderDto = new ShippingOrderDto();

        when(shippingOrderRepository.findById(any())).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(any())).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(shippingOrderDto);

        // When
        shippingOrderApplicationService.update(dto);

        // Then
        verify(shippingOrder).picked(dto);
        verify(shippingOrderRepository).update(shippingOrder);
        verify(businessEventDispatcher, times(0)).dispatch(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_shouldSuccessfullyUpdateOrders() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        // Create receiving task with items
        ReceivingTask receivingTask = mock(ReceivingTask.class);
        List<ReceivingTaskItem> receivingTaskItems = Arrays.asList(
            createReceivingTaskItem(orderId1),
            createReceivingTaskItem(orderId1),
            createReceivingTaskItem(orderId2)
        );
        when(receivingTask.getReceivingTaskItems()).thenReturn(receivingTaskItems);

        // Create shipping orders
        ShippingOrder order1 = mock(ShippingOrder.class);
        when(order1.getId()).thenReturn(orderId1);
        when(order1.getOrderNumber()).thenReturn("ORDER-1");
        ShippingOrder order2 = mock(ShippingOrder.class);
        when(order2.getId()).thenReturn(orderId2);
        when(order2.getOrderNumber()).thenReturn("ORDER-2");
        List<ShippingOrder> orders = Arrays.asList(order1, order2);

        // Mock repository responses
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(orders);
        when(shippingOrderRepository.update(any())).thenAnswer(i -> i.getArgument(0));

        // Mock DTO mapping
        ShippingOrderDto orderDto = new ShippingOrderDto();
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(orderDto);

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository).findByOrderIds(any());
        verify(order1).received(argThat(items -> items.size() == 2)); // Should receive 2 items for order1
        verify(order2).received(argThat(items -> items.size() == 1)); // Should receive 1 item for order2
        verify(shippingOrderRepository, times(2)).update(any());
        verify(shippingOrderDtoApplicationMapper, times(2)).domainToDto(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_whenReceivingTaskNotFound_shouldReturnEarly() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(null);

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository, never()).findByOrderIds(any());
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_whenNoItemsInReceivingTask_shouldReturnEarly() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        ReceivingTask receivingTask = mock(ReceivingTask.class);
        when(receivingTask.getReceivingTaskItems()).thenReturn(Collections.emptyList());
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository, never()).findByOrderIds(any());
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_whenNoOrdersFound_shouldReturnEarly() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        ReceivingTask receivingTask = mock(ReceivingTask.class);
        List<ReceivingTaskItem> receivingTaskItems = Collections.singletonList(
            createReceivingTaskItem(UUID.randomUUID())
        );
        when(receivingTask.getReceivingTaskItems()).thenReturn(receivingTaskItems);
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(Collections.emptyList());

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository).findByOrderIds(any());
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    private ReceivingTaskItem createReceivingTaskItem(UUID orderId) {
        ReceivingTaskItem item = mock(ReceivingTaskItem.class);
        when(item.getShippingOrderId()).thenReturn(orderId);
        return item;
    }

    @Test
    void validate_ShippingOrderNotFound() {
        ShippingOrderValidateCommand command = ShippingOrderValidateCommand.builder()
            .updateShippingOrderItemCommands(Collections.emptyList())
            .build();

        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(null);

        assertThrows(WmsBusinessException.class, () -> shippingOrderApplicationService.validate(shippingOrderId, command));
    }

    @Test
    void syncRouteInfo_WhenNoDeliveryOrdersFound_ShouldThrowException() {
        // Given
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        when(deliveryAdaptor.buildDeliveryTask(deliveryDate))
            .thenReturn(Collections.emptyList());

        // When & Then
        assertThrows(WmsBusinessException.class, () ->
                shippingOrderApplicationService.syncRouteInfo(deliveryDate),
            "No delivery orders found for the given delivery date: " + deliveryDate
        );

        verify(deliveryAdaptor).buildDeliveryTask(deliveryDate);
        verify(shippingOrderRepository, never()).findByDeliveryDate(anyString());
        verify(shippingOrderRepository, never()).updateAll(any());
    }

    @Test
    void syncRouteInfo_WhenNoShippingOrdersFound_ShouldLogAndReturn() {
        // Given
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        List<DeliveryOrderRouteInfoDto> deliveryOrderRouteInfoDtos = Arrays.asList(
            buildDeliveryOrderRouteInfoDto("M-ORDER001", "TRUCK001", "Driver1", UUID.randomUUID()),
            buildDeliveryOrderRouteInfoDto("M-ORDER002", "TRUCK002", "Driver2", UUID.randomUUID())
        );

        when(deliveryAdaptor.buildDeliveryTask(deliveryDate))
            .thenReturn(deliveryOrderRouteInfoDtos);
        when(shippingOrderRepository.findByDeliveryDate(deliveryDate.toString()))
            .thenReturn(Collections.emptyList());

        // When
        shippingOrderApplicationService.syncRouteInfo(deliveryDate);

        // Then
        verify(deliveryAdaptor).buildDeliveryTask(deliveryDate);
        verify(shippingOrderRepository).findByDeliveryDate(deliveryDate.toString());
        verify(shippingOrderRepository, never()).updateAll(any());
    }

    @Test
    void syncRouteInfo_WhenShippingOrdersFoundWithMatchingRouteInfo_ShouldUpdateShippingOrders() {
        // Given
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        UUID driverUserId = UUID.randomUUID();
        List<DeliveryOrderRouteInfoDto> deliveryOrderRouteInfoDtos = Arrays.asList(
            buildDeliveryOrderRouteInfoDto("M-ORDER001", "TRUCK001", "Driver1", driverUserId),
            buildDeliveryOrderRouteInfoDto("M-ORDER002", "TRUCK002", "Driver2", UUID.randomUUID())
        );

        List<ShippingOrder> shippingOrders = Arrays.asList(
            buildShippingOrderForRouteInfo("ORDER001"),
            buildShippingOrderForRouteInfo("ORDER002")
        );

        List<ShippingOrder> expectedUpdatedShippingOrders = Arrays.asList(
            buildShippingOrderForRouteInfo("ORDER001"),
            buildShippingOrderForRouteInfo("ORDER002")
        );

        when(deliveryAdaptor.buildDeliveryTask(deliveryDate))
            .thenReturn(deliveryOrderRouteInfoDtos);
        when(shippingOrderRepository.findByDeliveryDate(deliveryDate.toString()))
            .thenReturn(shippingOrders);
        when(shippingOrderRepository.updateAll(any()))
            .thenReturn(expectedUpdatedShippingOrders);

        // When
        shippingOrderApplicationService.syncRouteInfo(deliveryDate);

        // Then
        verify(deliveryAdaptor).buildDeliveryTask(deliveryDate);
        verify(shippingOrderRepository).findByDeliveryDate(deliveryDate.toString());
        verify(shippingOrderRepository).updateAll(any());
    }

    @Test
    void syncRouteInfo_WhenPartialMatchingRouteInfo_ShouldUpdateOnlyMatchingOrders() {
        // Given
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        UUID driverUserId = UUID.randomUUID();
        List<DeliveryOrderRouteInfoDto> deliveryOrderRouteInfoDtos = Collections.singletonList(
            buildDeliveryOrderRouteInfoDto("M-ORDER001", "TRUCK001", "Driver1", driverUserId)
            // ORDER002 has no matching route info
        );

        List<ShippingOrder> shippingOrders = Arrays.asList(
            buildShippingOrderForRouteInfo("ORDER001"),
            buildShippingOrderForRouteInfo("ORDER002")
        );

        List<ShippingOrder> expectedUpdatedShippingOrders = Collections.singletonList(
            buildShippingOrderForRouteInfo("ORDER001")
        );

        when(deliveryAdaptor.buildDeliveryTask(deliveryDate))
            .thenReturn(deliveryOrderRouteInfoDtos);
        when(shippingOrderRepository.findByDeliveryDate(deliveryDate.toString()))
            .thenReturn(shippingOrders);
        when(shippingOrderRepository.updateAll(any()))
            .thenReturn(expectedUpdatedShippingOrders);

        // When
        shippingOrderApplicationService.syncRouteInfo(deliveryDate);

        // Then
        verify(deliveryAdaptor).buildDeliveryTask(deliveryDate);
        verify(shippingOrderRepository).findByDeliveryDate(deliveryDate.toString());
        verify(shippingOrderRepository).updateAll(any());
    }

    private DeliveryOrderRouteInfoDto buildDeliveryOrderRouteInfoDto(String orderNumber, String truckNumber,
        String driverUserName, UUID driverUserId) {
        return DeliveryOrderRouteInfoDto.builder()
            .id(UUID.randomUUID())
            .orderNumber(orderNumber)
            .truckNumber(truckNumber)
            .driverUserName(driverUserName)
            .driverUserId(driverUserId)
            .status(DeliveryOrderStatus.CREATED)
            .deliveryDate(LocalDate.now().plusDays(1).toString())
            .build();
    }

    private ShippingOrder buildShippingOrderForRouteInfo(String orderNumber) {
        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber(orderNumber)
            .deliveryDate(LocalDate.now().plusDays(1))
            .build();
    }

    @Test
    void validateHighValueItems_whenMultipleOrders_thenUpdateEachAndSendEvents() {
        // Arrange
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        UUID itemId1 = UUID.randomUUID();
        UUID itemId2 = UUID.randomUUID();

        ShippingOrder order1 = ShippingOrder.builder().id(orderId1).build();
        order1.setStatus(ShippingOrderStatus.VALIDATED);
        order1.setShippingOrderItems(List.of(
            com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem.builder()
                .id(itemId1)
                .shippingOrderId(orderId1)
                .qty(5)
                .build()
        ));

        ShippingOrder order2 = ShippingOrder.builder().id(orderId2).build();
        order2.setStatus(ShippingOrderStatus.VALIDATED);
        order2.setShippingOrderItems(List.of(
            com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem.builder()
                .id(itemId2)
                .shippingOrderId(orderId2)
                .qty(8)
                .build()
        ));

        when(shippingOrderRepository.findById(orderId1)).thenReturn(order1);
        when(shippingOrderRepository.findById(orderId2)).thenReturn(order2);
        when(shippingOrderRepository.updateAll(any())).thenAnswer(invocation -> invocation.getArgument(0));

        List<UpdateShippingOrderItemCommand> updateCommands = List.of(
            UpdateShippingOrderItemCommand.builder()
                .id(itemId1)
                .shippingOrderId(orderId1)
                .validatedQty(3)
                .reasonCode("RC1")
                .build(),
            UpdateShippingOrderItemCommand.builder()
                .id(itemId2)
                .shippingOrderId(orderId2)
                .validatedQty(4)
                .reasonCode(null)
                .build()
        );
        ShippingOrderHighValueItemValidateCommand command = ShippingOrderHighValueItemValidateCommand.builder()
            .updateShippingOrderItemCommands(updateCommands)
            .build();

        ShippingOrderDto dto1 = ShippingOrderDto.builder().id(orderId1).status(ShippingOrderStatus.VALIDATED).build();
        ShippingOrderDto dto2 = ShippingOrderDto.builder().id(orderId2).status(ShippingOrderStatus.VALIDATED).build();
        when(shippingOrderDtoApplicationMapper.domainToDtos(any())).thenReturn(List.of(dto1, dto2));

        // Act
        List<ShippingOrderDto> result = shippingOrderApplicationService.validateHighValueItems(command);

        // Assert
        verify(shippingOrderRepository, times(1)).updateAll(argThat(list ->
            list.size() == 2 &&
                list.stream().allMatch(o -> o.getStatus() == ShippingOrderStatus.VALIDATED) &&
                list.stream().anyMatch(o -> o.getId().equals(orderId1) &&
                    o.getShippingOrderItems().getFirst().getValidatedQty().equals(3)) &&
                list.stream().anyMatch(o -> o.getId().equals(orderId2) &&
                    o.getShippingOrderItems().getFirst().getValidatedQty().equals(4))
        ));
        verify(shippingOrderService, times(2)).saveBusinessEvent(any(), anyBoolean(), anyBoolean());
        verify(shippingOrderDtoApplicationMapper, times(1)).domainToDtos(any());
        verify(businessEventDispatcher, times(0)).dispatch(any());
        org.junit.jupiter.api.Assertions.assertEquals(2, result.size());
    }

    @Test
    void validateHighValueItems_whenOrderNotFound_thenThrow() {
        // Arrange
        UUID orderId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        when(shippingOrderRepository.findById(orderId)).thenReturn(null);

        ShippingOrderHighValueItemValidateCommand command = ShippingOrderHighValueItemValidateCommand.builder()
            .updateShippingOrderItemCommands(List.of(
                UpdateShippingOrderItemCommand.builder().id(itemId).shippingOrderId(orderId).validatedQty(1).build()
            ))
            .build();

        // Act & Assert
        assertThrows(WmsBusinessException.class, () -> shippingOrderApplicationService.validateHighValueItems(command));
        verify(shippingOrderRepository, never()).updateAll(any());
    }

    @Test
    void validateHighValueItems_whenInvalidStatus_thenThrow() {
        // Arrange
        UUID orderId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();

        ShippingOrder order = ShippingOrder.builder().id(orderId).build();
        order.setStatus(ShippingOrderStatus.OPEN);
        order.setShippingOrderItems(List.of(
            com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem.builder()
                .id(itemId)
                .shippingOrderId(orderId)
                .qty(2)
                .build()
        ));
        when(shippingOrderRepository.findById(orderId)).thenReturn(order);

        ShippingOrderHighValueItemValidateCommand command = ShippingOrderHighValueItemValidateCommand.builder()
            .updateShippingOrderItemCommands(List.of(
                UpdateShippingOrderItemCommand.builder().id(itemId).shippingOrderId(orderId).validatedQty(1).build()
            ))
            .build();

        // Act & Assert
        assertThrows(WmsBusinessException.class, () -> shippingOrderApplicationService.validateHighValueItems(command));
        verify(shippingOrderRepository, never()).updateAll(any());
    }

}