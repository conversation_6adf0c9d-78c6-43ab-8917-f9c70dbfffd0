package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.view.SearchShippingOrderView;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.utils.ShippingOrderResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchShippingOrderResourceIT extends AbstractIT {

    @Autowired
    ShippingOrderResourceApi shippingOrderResourceApi;

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void searchShippingOrders_withParameters_returnsResult() throws Exception {

        shippingOrderRepository.deleteAll();

        ShopifyOrderDto shopifyOrderDto1 = buildShopifyOrderDto();
        ShopifyOrderDto shopifyOrderDto2 = buildShopifyOrderDto();
        shopifyWebhookResourceApi.webhook(shopifyOrderDto1);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto2);

        Optional<Location> shippingBig = locationRepository.findAll()
            .stream()
            .findFirst();
        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto1.getName());
        shippingOrder.setDriverUserId(UUID.randomUUID());
        shippingOrder.setDriverUserName(RandomStringUtils.randomAlphabetic(10));
        shippingOrder.setTruckNumber(RandomStringUtils.randomAlphabetic(10));
        shippingOrder.setBreakdownLocation(shippingBig.get());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);

        Result<ShippingOrderDto> shippingOrderDtoResult = shippingOrderResourceApi.searchShippingOrders(
            null,
            shopifyOrderDto1.getTags().split(",")[0],
            shippingBig.get().getName(),
            null,
            null);

        assertEquals(1, shippingOrderDtoResult.getTotalCount());
        assertEquals(1, shippingOrderDtoResult.getData().size());
        assertEquals(shippingBig.get().getName(), shippingOrderDtoResult.getData().getFirst().getBreakdownLocation().getName());

        Result<SearchShippingOrderView> searchShippingOrderViewResult = shippingOrderResourceApi.searchShippingOrdersV2(
            null,
            shopifyOrderDto1.getTags().split(",")[0],
            shippingBig.get().getName(),
            null,
            null,
            null,
            null,
            null);
        assertEquals(1, searchShippingOrderViewResult.getTotalCount());
        assertEquals(1, searchShippingOrderViewResult.getData().size());
        assertEquals(shippingBig.get().getName(), searchShippingOrderViewResult.getData().getFirst().getBreakdownName());
        assertEquals(100, searchShippingOrderViewResult.getData().getFirst().getOriginalTotalQty());
        assertEquals(100, searchShippingOrderViewResult.getData().getFirst().getCurrentTotalQty());

        Result<SearchShippingOrderView> searchShippingOrderViewByDriverUserId = shippingOrderResourceApi.searchShippingOrdersV2(
            null,
            null,
            null,
            null,
            savedShippingOrder.getDriverUserId(),
            null,
            null,
            null);

        assertEquals(1, searchShippingOrderViewByDriverUserId.getTotalCount());
        assertEquals(1, searchShippingOrderViewByDriverUserId.getData().size());
        assertEquals(savedShippingOrder.getDriverUserName(),
            searchShippingOrderViewByDriverUserId.getData().getFirst().getDriverUserName());
        assertEquals(savedShippingOrder.getTruckNumber(),
            searchShippingOrderViewByDriverUserId.getData().getFirst().getTruckNumber());

        Result<SearchShippingOrderView> searchShippingOrderViewByTruckNumber = shippingOrderResourceApi.searchShippingOrdersV2(
            null,
            null,
            null,
            null,
            null,
            savedShippingOrder.getTruckNumber(),
            null,
            null);
        assertEquals(1, searchShippingOrderViewByTruckNumber.getTotalCount());
        assertEquals(1, searchShippingOrderViewByTruckNumber.getData().size());
        assertEquals(savedShippingOrder.getTruckNumber(),
            searchShippingOrderViewByTruckNumber.getData().getFirst().getTruckNumber());
    }

}