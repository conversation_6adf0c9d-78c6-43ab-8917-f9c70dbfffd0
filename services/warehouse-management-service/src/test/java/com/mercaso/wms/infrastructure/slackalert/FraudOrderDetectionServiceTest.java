package com.mercaso.wms.infrastructure.slackalert;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.NoteAttributeDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShippingAddressDto;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.customeraddress.CustomerAddressRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class FraudOrderDetectionServiceTest {

    @Mock
    private CustomerAddressRepository customerAddressRepository;

    @InjectMocks
    private FraudOrderDetectionService fraudOrderDetectionService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(fraudOrderDetectionService, "slackWebhook", "https://hooks.slack.com/test");
        ReflectionTestUtils.setField(fraudOrderDetectionService, "enable", true);
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudIps", Arrays.asList("***********", "********"));
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudDeviceIds", Arrays.asList("device123", "device456"));
        ReflectionTestUtils.setField(fraudOrderDetectionService,
            "fraudPhoneNumbers",
            Arrays.asList("+1234567890", "+0987654321"));
    }

    @Test
    void when_detectFraudOrder_and_noteAttributesEmpty_then_noAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = ShopifyOrderDto.builder()
            .name("ORDER-001")
            .noteAttributes(Collections.emptyList())
            .build();

        // When
        fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

        // Then
        verify(customerAddressRepository, never()).findByLatitudeAndLongitude(any(), any());
    }

    @Test
    void when_detectFraudOrder_and_highRiskIP_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********", "safe_device");

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                eq("https://hooks.slack.com/test"), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_highRiskDeviceId_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********00", "device123");

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_newUser_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********00", "safe_device");

        when(customerAddressRepository.findByFirstNameAndLastNameAndPhone(anyString(), anyString(), anyString()))
            .thenReturn(Collections.emptyList());
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_newAddress_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********00", "safe_device");

        when(customerAddressRepository.findByFirstNameAndLastNameAndPhone(anyString(), anyString(), anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.emptyList());
        when(customerAddressRepository.findByCityAndPostalCodeAndAddressOne(anyString(), anyString(), anyString()))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_achPayment_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoWithPayment();

        when(customerAddressRepository.findByFirstNameAndLastNameAndPhone(anyString(), anyString(), anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_slackDisabled_then_noAlert() {
        // Given
        ReflectionTestUtils.setField(fraudOrderDetectionService, "enable", false);
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********", "safe_device");

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_nullShippingAddress_then_noException() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .shippingAddress(null)
            .noteAttributes(Arrays.asList(
                new NoteAttributeDto("ip", "***********"),
                new NoteAttributeDto("device_id", "safe_device"),
                new NoteAttributeDto("payment_type", "CREDIT_CARD")
            ))
            .build();

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not throw exception and should send alert for high risk IP
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_fraudListsEmpty_then_noHighRiskAlert() {
        // Given
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudIps", Collections.emptyList());
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudDeviceIds", Collections.emptyList());
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudPhoneNumbers", Collections.emptyList());

        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********", "device123");

        when(customerAddressRepository.findByFirstNameAndLastNameAndPhone(anyString(), anyString(), anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send high risk alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    private ShippingOrder createTestShippingOrder() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .build();
    }

    private CustomerAddress createTestCustomerAddress() {
        return CustomerAddress.builder()
            .id(UUID.randomUUID())
            .firstName("John")
            .lastName("Doe")
            .phone("+1234567890")
            .city("New York")
            .postalCode("10001")
            .addressOne("123 Main St")
            .latitude(new BigDecimal("40.7128"))
            .longitude(new BigDecimal("-74.0060"))
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDto(String ip, String deviceId) {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", ip),
            new NoteAttributeDto("device_id", deviceId),
            new NoteAttributeDto("payment_type", "CREDIT_CARD")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1234567890");

        return ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .note("Test order")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDtoWithPayment() {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", "***********00"),
            new NoteAttributeDto("device_id", "safe_device"),
            new NoteAttributeDto("payment_type", "ACH_DIRECT_DEBIT")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1234567890");

        return ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .note("Test order")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }
}