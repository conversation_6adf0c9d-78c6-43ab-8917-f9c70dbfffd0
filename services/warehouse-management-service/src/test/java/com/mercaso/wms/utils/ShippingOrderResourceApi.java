package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderHighValueItemValidateCommand;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderValidateCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderItemDto;
import com.mercaso.wms.application.dto.view.SearchShippingOrderView;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class ShippingOrderResourceApi extends IntegrationTestRestUtil {

    public static final String GET_SHIPPING_ORDER_BY_ID_URL = "/query/shipping-orders/%s";
    private static final String SEARCH_SHIPPING_ORDERS_URL = "/search/shipping-orders";
    private static final String PICKED_SHIPPING_ORDERS_EXPORT_URL = "/shipping-orders/%s/picked-shipping-orders-export";
    public static final String VALIDATE_SHIPPING_ORDER_URL = "/shipping-orders/%s/validate";
    private static final String SEARCH_SHIPPING_ORDERS_URL_V2 = "/search/shipping-orders/v2";
    private static final String SYNC_ROUTE_INFO = "/shipping-orders/%s/sync-route-info";
    private static final String GET_SHIPPING_ORDER_HIGH_VALUE_ITEMS = "/query/shipping-orders/high-value-items";
    private static final String HIGH_VALUE_ITEM_VALIDATE_URL = "/shipping-orders/high-value-items/validate";

    public ShippingOrderResourceApi(Environment environment) {
        super(environment);
    }

    public ShippingOrderDto getShippingOrder(UUID id) {
        return getEntity(String.format(GET_SHIPPING_ORDER_BY_ID_URL, id), ShippingOrderDto.class).getBody();
    }

    public Result<ShippingOrderDto> searchShippingOrders(
        String orderNumber,
        String deliveryDate,
        String breakdownName,
        String[] statuses,
        SortType sort) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_SHIPPING_ORDERS_URL + "?page=1&pageSize=20");
        if (orderNumber != null) {
            url.append("&orderNumber=").append(orderNumber);
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (breakdownName != null) {
            url.append("&breakdownName=").append(breakdownName);
        }
        if (statuses != null) {
            url.append("&statuses=").append(String.join(",", statuses));
        }
        if (sort != null) {
            url.append("&sort=").append(sort);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<ShippingOrderDto>>() {
        });
    }

    public Result<SearchShippingOrderView> searchShippingOrdersV2(
        String orderNumber,
        String deliveryDate,
        String breakdownName,
        String[] statuses,
        UUID driverUserId,
        String truckNumber,
        Boolean includeHighValueItems,
        SortType sort) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_SHIPPING_ORDERS_URL_V2 + "?page=1&pageSize=20");
        if (orderNumber != null) {
            url.append("&orderNumber=").append(orderNumber);
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (breakdownName != null) {
            url.append("&breakdownName=").append(breakdownName);
        }
        if (statuses != null) {
            url.append("&statuses=").append(String.join(",", statuses));
        }
        if (driverUserId != null) {
            url.append("&driverUserId=").append(driverUserId);
        }
        if (truckNumber != null) {
            url.append("&truckNumber=").append(truckNumber);
        }
        if (includeHighValueItems != null) {
            url.append("&hasHighValueItems=").append(includeHighValueItems);
        }
        if (sort != null) {
            url.append("&sort=").append(sort);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<SearchShippingOrderView>>() {
        });
    }

    public byte[] pickedShippingOrdersExport(String deliveryDate) {
        String url = String.format(PICKED_SHIPPING_ORDERS_EXPORT_URL, deliveryDate);
        ResponseEntity<byte[]> response = getEntity(url, byte[].class);
        return response.getBody();
    }

    public ShippingOrderDto validateShippingOrder(UUID id, ShippingOrderValidateCommand command) throws JsonProcessingException {
        String url = String.format(VALIDATE_SHIPPING_ORDER_URL, id);
        return updateEntity(url, command, ShippingOrderDto.class);
    }

    public List<ShippingOrderDto> validateHighValueItems(ShippingOrderHighValueItemValidateCommand command) throws IOException {
        return updateEntities(HIGH_VALUE_ITEM_VALIDATE_URL, command, ShippingOrderDto.class);
    }

    public void syncRouteInfo(LocalDate localDate) throws JsonProcessingException {
        updateEntity(String.format(SYNC_ROUTE_INFO, localDate.toString()), null, Void.class);
    }

    public List<ShippingOrderItemDto> findHighValueItemsBy(UUID driverUserId, String deliveryDate) throws IOException {
        String url = GET_SHIPPING_ORDER_HIGH_VALUE_ITEMS + "?driverUserId=" + driverUserId + "&deliveryDate=" + deliveryDate;
        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<List<ShippingOrderItemDto>>() {
        });
    }

}
